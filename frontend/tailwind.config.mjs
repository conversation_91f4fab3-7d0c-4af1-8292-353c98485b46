/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ['class'],
    content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
  	extend: {
  		fontFamily: {
  			anton: [
  				'Anton',
  				'sans-serif'
  			],
  			inter: [
  				'Inter',
  				'sans-serif'
  			],
  			poppins: [
  				'Poppins',
  				'sans-serif'
  			],
  			roboto: [
  				'Roboto',
  				'sans-serif'
  			]
  		},
  		colors: {
  			primary: {
  				'50': '#ECFDF5',
  				'100': '#D1FAE5',
  				'200': '#A7F3D0',
  				'300': '#6EE7B7',
  				'400': '#34D399',
  				'500': '#10B981',
  				'600': '#059669',
  				'700': '#047857',
  				'800': '#065F46',
  				'900': '#064E3B',
  				DEFAULT: 'hsl(var(--primary))',
  				green: '#059669',
  				greenLight: '#10B981',
  				greenDark: '#047857',
  				greenSubtle: '#D1FAE5',
  				greenMuted: '#6EE7B7',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				'50': '#FFFBEB',
  				'100': '#FEF3C7',
  				'200': '#FDE68A',
  				'300': '#FCD34D',
  				'400': '#FBBF24',
  				'500': '#F59E0B',
  				'600': '#D97706',
  				'700': '#B45309',
  				'800': '#92400E',
  				'900': '#78350F',
  				DEFAULT: 'hsl(var(--secondary))',
  				yellow: '#F59E0B',
  				yellowLight: '#FBBF24',
  				yellowDark: '#D97706',
  				yellowSubtle: '#FEF3C7',
  				yellowMuted: '#FCD34D',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			neutral: {
  				white: '#FFFFFF',
  				lightGray: '#F9FAFB',
  				mediumGray: '#E5E7EB',
  				darkGray: '#6B7280',
  				charcoal: '#374151',
  				black: '#1F2937'
  			},
  			slate: {
  				'50': '#F8FAFC',
  				'100': '#F1F5F9',
  				'200': '#E2E8F0',
  				'300': '#CBD5E1',
  				'400': '#94A3B8',
  				'500': '#64748B',
  				'600': '#475569',
  				'700': '#334155',
  				'800': '#1E293B',
  				'900': '#0F172A'
  			},
  			accent: {
  				'50': '#fdf4ff',
  				'100': '#fae8ff',
  				'200': '#f5d0fe',
  				'300': '#f0abfc',
  				'400': '#e879f9',
  				'500': '#d946ef',
  				'600': '#c026d3',
  				'700': '#a21caf',
  				'800': '#86198f',
  				'900': '#701a75',
  				'950': '#4a044e',
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		animation: {
  			'spin-slow': 'spin 3s linear infinite',
  		    'star-movement-bottom': 'star-movement-bottom linear infinite alternate',
  		    'star-movement-top': 'star-movement-top linear infinite alternate',
  		},
  		  keyframes: {
  		    'star-movement-bottom': {
  		      '0%': { transform: 'translate(0%, 0%)', opacity: '1' },
  		      '100%': { transform: 'translate(-100%, 0%)', opacity: '0' },
  		    },
  		    'star-movement-top': {
  		      '0%': { transform: 'translate(0%, 0%)', opacity: '1' },
  		      '100%': { transform: 'translate(100%, 0%)', opacity: '0' },
  		    },
  		  },
  		boxShadow: {
  			subtle: '0 2px 4px rgba(0, 0, 0, 0.05)',
  			medium: '0 4px 8px rgba(0, 0, 0, 0.1)',
  			large: '0 10px 20px rgba(0, 0, 0, 0.15)',
  			card: '0 8px 16px rgba(0,0,0,0.1)',
  			glassmorphism: '0 8px 32px rgba(0, 0, 0, 0.1)',
  			glow: '0 0 20px rgba(5, 150, 105, 0.3)'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		backdropBlur: {
  			'xs': '2px',
  			'sm': '4px',
  			'md': '8px',
  			'lg': '12px',
  			'xl': '16px',
  			'2xl': '24px',
  			'3xl': '32px'
  		},
  		spacing: {
  			'18': '4.5rem',
  			'88': '22rem',
  			'128': '32rem'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}
