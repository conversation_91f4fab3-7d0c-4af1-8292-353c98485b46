// Payment and transaction type definitions for Trodoo with Paystack integration

// Paystack payment types
export interface PaystackConfig {
  publicKey: string;
  currency: 'NGN' | 'USD' | 'GHS' | 'ZAR';
  channels: ('card' | 'bank' | 'ussd' | 'qr' | 'mobile_money' | 'bank_transfer')[];
}

export interface PaystackPaymentData {
  email: string;
  amount: number; // Amount in kobo (for NGN) or cents
  currency: string;
  reference: string;
  callback_url?: string;
  metadata?: {
    booking_id: string;
    venue_id: string;
    renter_id: string;
    owner_id: string;
    custom_fields?: Array<{
      display_name: string;
      variable_name: string;
      value: string;
    }>;
  };
  channels?: string[];
  split_code?: string;
}

export interface PaystackResponse {
  status: boolean;
  message: string;
  data: {
    authorization_url: string;
    access_code: string;
    reference: string;
  };
}

export interface PaystackVerificationResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    domain: string;
    status: 'success' | 'failed' | 'abandoned';
    reference: string;
    amount: number;
    message: string;
    gateway_response: string;
    paid_at: string;
    created_at: string;
    channel: string;
    currency: string;
    ip_address: string;
    metadata: {
      booking_id: string;
      venue_id: string;
      renter_id: string;
      owner_id: string;
    };
    customer: {
      id: number;
      first_name: string;
      last_name: string;
      email: string;
      customer_code: string;
      phone: string;
    };
    authorization: {
      authorization_code: string;
      bin: string;
      last4: string;
      exp_month: string;
      exp_year: string;
      channel: string;
      card_type: string;
      bank: string;
      country_code: string;
      brand: string;
      reusable: boolean;
      signature: string;
    };
  };
}

// Payment flow types
export interface PaymentInitialization {
  booking_id: string;
  amount: number;
  currency: string;
  customer_email: string;
  reference: string;
  callback_url: string;
  metadata: Record<string, string | number>;
}

export interface PaymentVerification {
  reference: string;
  status: 'success' | 'failed' | 'abandoned';
  amount: number;
  currency: string;
  paid_at?: string;
  gateway_response?: string;
  customer_details?: {
    email: string;
    phone?: string;
    name?: string;
  };
}

export interface PaymentDetails {
  id: string;
  booking_id: string;
  amount: number;
  currency: string;
  reference: string;
  status: 'pending' | 'processing' | 'success' | 'failed' | 'abandoned';
  gateway: 'paystack';
  gateway_response?: string;
  paid_at?: string;
  metadata?: Record<string, string | number | boolean>;
  created: string;
  updated: string;
}

// Payout types
export interface PayoutRecipient {
  type: 'nuban' | 'mobile_money' | 'basa';
  name: string;
  account_number: string;
  bank_code: string;
  currency: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface PaystackTransferData {
  source: 'balance';
  amount: number;
  recipient: string; // recipient code
  reason: string;
  currency: string;
  reference?: string;
  metadata?: {
    booking_id: string;
    venue_id: string;
    owner_id: string;
  };
}

export interface PaystackTransferResponse {
  status: boolean;
  message: string;
  data: {
    reference: string;
    integration: number;
    domain: string;
    amount: number;
    currency: string;
    source: string;
    reason: string;
    recipient: number;
    status: 'pending' | 'success' | 'failed' | 'processing';
    transfer_code: string;
    id: number;
    createdAt: string;
    updatedAt: string;
  };
}

export interface Payout {
  id: string;
  booking_id: string;
  owner_id: string;
  amount: number;
  platform_fee: number;
  net_amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  paystack_transfer_code?: string;
  paystack_reference?: string;
  recipient_code: string;
  reason?: string;
  processed_at?: string;
  failed_reason?: string;
  created: string;
  updated: string;
}

// Transaction summary types
export interface TransactionSummary {
  booking_id: string;
  venue_title: string;
  total_amount: number;
  platform_fee: number;
  payout_amount: number;
  currency: string;
  payment_status: string;
  payout_status?: string;
  dates: {
    booking_start: string;
    booking_end: string;
    payment_date?: string;
    payout_date?: string;
  };
}

// Fee calculation types
export interface FeeCalculation {
  subtotal: number;
  platform_fee_rate: number;
  platform_fee: number;
  payment_processing_fee: number;
  total_amount: number;
  payout_amount: number;
  currency: string;
}

// Payment form types
export interface PaymentFormData {
  email: string;
  amount: number;
  currency: string;
  accept_terms: boolean;
}

export interface PaymentValidationErrors {
  email?: string;
  amount?: string;
  currency?: string;
  accept_terms?: string;
  general?: string;
}

// API response types
export interface PaymentActionResponse {
  success: boolean;
  payment?: PaymentDetails;
  authorization_url?: string;
  reference?: string;
  error?: string;
  message?: string;
}

export interface PayoutActionResponse {
  success: boolean;
  payout?: Payout;
  transfer_code?: string;
  error?: string;
  message?: string;
}

// Webhook types
export interface PaystackWebhookEvent {
  event: 'charge.success' | 'transfer.success' | 'transfer.failed' | 'transfer.reversed';
  data: PaystackVerificationResponse['data'] | PaystackTransferResponse['data'];
}

export interface WebhookValidation {
  signature: string;
  body: string;
  secret: string;
  isValid: boolean;
}
