// Review-related type definitions for Trodoo
import type { RecordModel } from 'pocketbase';

export interface Review extends RecordModel {
  booking: string; // Booking ID
  renter: string; // User ID
  venue: string; // Venue ID
  rating: number; // 1-5 star rating
  comment: string; // Review text
  owner_response?: string; // Optional owner response
  created: string;
  updated: string;
}

export interface ReviewWithExpanded extends Review {
  expand?: {
    booking?: {
      id: string;
      venue: {
        id: string;
        title: string;
      };
      renter: {
        id: string;
        name: string;
        email: string;
        avatar?: string;
      };
      owner: {
        id: string;
        name: string;
        email: string;
        avatar?: string;
      };
      start_date: string;
      end_date: string;
      status: string;
    };
    renter?: {
      id: string;
      name: string;
      email: string;
      avatar?: string;
    };
    venue?: {
      id: string;
      title: string;
      owner: string;
    };
  };
}

export interface ReviewFormData {
  booking: string;
  renter: string;
  venue: string;
  rating: number;
  comment: string;
}

export interface ReviewListResponse {
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
  items: ReviewWithExpanded[];
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface OwnerResponseData {
  owner_response: string;
}

export interface ReviewPrompt {
  bookingId: string;
  venueId: string;
  venueName: string;
  canReview: boolean;
  hasReviewed: boolean;
}

// API response types
export interface CreateReviewResponse {
  success: boolean;
  review?: Review;
  error?: string;
}

export interface GetReviewsResponse {
  success: boolean;
  reviews?: ReviewListResponse;
  error?: string;
}

export interface UpdateReviewResponse {
  success: boolean;
  review?: Review;
  error?: string;
}

export interface GetReviewStatsResponse {
  success: boolean;
  stats?: ReviewStats;
  error?: string;
}

export interface GetReviewPromptsResponse {
  success: boolean;
  prompts?: ReviewPrompt[];
  error?: string;
}
