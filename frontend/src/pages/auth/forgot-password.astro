---
import Layout from '../../components/core/Layout.astro';
---

<Layout
  title="Reset Password - Trodoo"
  description="Reset your Trodoo account password to regain access to your account."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex font-['Roboto','Helvetica_Neue',Arial,sans-serif]">
    <!-- Left Column - Desktop Only -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden" style="background: linear-gradient(135deg, #059669 0%, #10B981 100%);">
      <!-- GridMotion Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.3) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.3) 2px, transparent 0); background-size: 100px 100px;"></div>
      </div>

      <!-- Glassmorphism Overlay -->
      <div class="absolute inset-0" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(16px);"></div>

      <!-- Content -->
      <div class="relative z-10 flex flex-col justify-center px-12 py-16">
        <div class="max-w-md">
          <!-- Logo with Back Button -->
          <div class="flex items-center justify-between mb-8">
            <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
              <h1 class="text-4xl font-bold text-white font-['Anton',sans-serif] tracking-wide">Trodoo</h1>
            </a>
            <button
              onclick="window.location.href='/auth/login'"
              class="flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105"
              style="background: rgba(255, 255, 255, 0.2); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.3);"
              onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
              onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'"
            >
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span class="text-white font-medium text-sm">Back to Login</span>
            </button>
          </div>

          <!-- Tagline -->
          <h2 class="text-4xl font-bold text-white mb-6 leading-tight font-['Poppins','Helvetica_Neue',Arial,sans-serif]">
            Reset Your Password
          </h2>

          <p class="text-xl text-white/90 mb-8 leading-relaxed font-normal">
            Don't worry, it happens to the best of us. Enter your email address and we'll send you a link to reset your password.
          </p>

          <!-- Features -->
          <div class="space-y-4">
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Secure password reset process</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Email link expires in 1 hour</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Quick and easy process</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Decorative GridMotion Pattern -->
      <div class="absolute bottom-0 right-0 w-64 h-64 opacity-20">
        <svg viewBox="0 0 200 200" class="w-full h-full">
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(5, 150, 105, 0.5)" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="200" height="200" fill="url(#grid)" />
        </svg>
      </div>
    </div>

    <!-- Right Column - Form -->
    <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12 bg-gradient-to-br from-slate-50 to-white relative">
      <!-- Background Grid Pattern -->
      <div class="absolute inset-0 opacity-30 pointer-events-none">
        <div
          class="absolute inset-0"
          style="background-image: radial-gradient(circle at 1px 1px, rgba(5, 150, 105, 0.1) 1px, transparent 0); background-size: 40px 40px;"
        ></div>
      </div>

      <div class="max-w-md w-full space-y-8 relative z-10">
        <!-- Mobile Logo -->
        <div class="lg:hidden text-center">
          <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
            <h1 class="text-3xl font-bold text-emerald-600 tracking-wide" style="font-family: 'Anton', sans-serif;">Trodoo</h1>
          </a>
        </div>

        <!-- Header -->
        <div class="text-center">
          <h2 class="text-4xl font-bold leading-tight text-slate-900" style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;">
            Forgot Password?
          </h2>
          <p class="mt-3 text-base leading-relaxed text-slate-600" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
            Remember your password?
            <a href="/auth/login" class="font-medium text-emerald-600 hover:text-emerald-700 ml-1 transition-colors duration-300 hover:scale-105 inline-block">
              Sign In
            </a>
          </p>
        </div>

        <!-- Reset Form Container -->
        <div class="bg-white rounded-2xl p-8 border border-slate-200 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
          <div id="reset-form-container">
            <!-- React component will be mounted here -->
          </div>
        </div>

        <!-- Additional Links -->
        <div class="text-center space-y-3">
          <div class="text-sm text-slate-600" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
            Need help?
            <a href="/contact" class="text-emerald-600 hover:text-emerald-700 ml-1 transition-colors duration-300 font-medium">
              Contact support
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  import { authActions } from '../../lib/state.ts';

  // Enhanced forgot password form
  document.addEventListener('DOMContentLoaded', async () => {
    const container = document.getElementById('reset-form-container');
    if (!container) return;

    let isLoading = false;
    let error = '';
    let successMessage = '';
    let emailSent = false;

    const renderForm = () => {
      if (emailSent) {
        // Success state
        container.innerHTML = `
          <div class="text-center space-y-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-2" style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;">
                Check Your Email
              </h3>
              <p class="text-slate-600 mb-4" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                We've sent a password reset link to your email address. Click the link in the email to reset your password.
              </p>
              <p class="text-sm text-slate-500" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                Didn't receive the email? Check your spam folder or try again.
              </p>
            </div>

            <div class="space-y-3">
              <button
                onclick="window.location.reload()"
                class="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-medium text-base py-3 px-6 rounded-lg transition-all duration-300 hover:scale-105"
                style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
              >
                Send Another Email
              </button>
              
              <a
                href="/auth/login"
                class="block w-full text-center bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium text-base py-3 px-6 rounded-lg transition-all duration-300 hover:scale-105"
                style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
              >
                Back to Login
              </a>
            </div>
          </div>
        `;
      } else {
        // Form state
        container.innerHTML = `
          <form id="reset-form" class="space-y-6">
            <div>
              <label for="email" class="block text-sm font-medium text-slate-700 mb-2" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                class="w-full px-4 py-3 border border-slate-300 rounded-lg bg-white text-base focus:outline-none focus:ring-2 focus:ring-emerald-600 focus:ring-opacity-20 focus:border-emerald-600 transition-all duration-300 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
                placeholder="Enter your email address"
                style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
                ${isLoading ? 'disabled' : ''}
              />
              <p class="mt-2 text-sm text-slate-500" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                We'll send a password reset link to this email address.
              </p>
            </div>

            ${successMessage ? `
              <div class="p-4 bg-green-50 border border-green-200 rounded-lg animate-fade-in">
                <p class="text-sm text-green-600">${successMessage}</p>
              </div>
            ` : ''}

            ${error ? `
              <div class="p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
                <p class="text-sm text-red-600">${error}</p>
              </div>
            ` : ''}

            <button
              type="submit"
              ${isLoading ? 'disabled' : ''}
              class="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold text-base py-3 px-6 rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex justify-center items-center"
              style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;"
            >
              ${isLoading ? `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending Reset Link...
              ` : 'Send Reset Link'}
            </button>
          </form>
        `;

        // Attach event listener
        const form = document.getElementById('reset-form') as HTMLFormElement;
        if (form) {
          form.addEventListener('submit', handleSubmit);
        }
      }
    };

    const handleSubmit = async (e: Event) => {
      e.preventDefault();

      const formData = new FormData(e.target as HTMLFormElement);
      const email = formData.get('email') as string;

      if (!email) {
        error = 'Please enter your email address.';
        successMessage = '';
        renderForm();
        return;
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        error = 'Please enter a valid email address.';
        successMessage = '';
        renderForm();
        return;
      }

      isLoading = true;
      error = '';
      successMessage = '';
      renderForm();

      try {
        const result = await authActions.requestPasswordReset(email);

        if (result.success) {
          emailSent = true;
          isLoading = false;
          renderForm();
        } else {
          error = result.error || 'Failed to send reset email. Please try again.';
          successMessage = '';
          isLoading = false;
          renderForm();
        }
      } catch (err) {
        console.error('Password reset error:', err);
        error = 'An unexpected error occurred. Please try again.';
        successMessage = '';
        isLoading = false;
        renderForm();
      }
    };

    // Initial render
    renderForm();
  });
</script>

<style>
  /* Additional styles for the forgot password page */
  .bg-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.1) 2px, transparent 0),
      radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
  }
</style>
