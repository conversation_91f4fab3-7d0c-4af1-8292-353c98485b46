---
import Layout from '../../components/core/Layout.astro';

// Get the token from URL parameters
const url = new URL(Astro.request.url);
const token = url.searchParams.get('token');

// If no token, redirect to forgot password page
if (!token) {
  return Astro.redirect('/auth/forgot-password');
}
---

<Layout
  title="Reset Password - Trodoo"
  description="Set your new Trodoo account password."
  showHeader={false}
  showFooter={false}
>
  <div class="min-h-screen flex font-['Roboto','Helvetica_Neue',Arial,sans-serif]">
    <!-- Left Column - Desktop Only -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden" style="background: linear-gradient(135deg, #059669 0%, #10B981 100%);">
      <!-- GridMotion Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.3) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.3) 2px, transparent 0); background-size: 100px 100px;"></div>
      </div>

      <!-- Glassmorphism Overlay -->
      <div class="absolute inset-0" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(16px);"></div>

      <!-- Content -->
      <div class="relative z-10 flex flex-col justify-center px-12 py-16">
        <div class="max-w-md">
          <!-- Logo -->
          <div class="mb-8">
            <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
              <h1 class="text-4xl font-bold text-white font-['Anton',sans-serif] tracking-wide">Trodoo</h1>
            </a>
          </div>

          <!-- Tagline -->
          <h2 class="text-4xl font-bold text-white mb-6 leading-tight font-['Poppins','Helvetica_Neue',Arial,sans-serif]">
            Set New Password
          </h2>

          <p class="text-xl text-white/90 mb-8 leading-relaxed font-normal">
            You're almost done! Create a strong, secure password for your account.
          </p>

          <!-- Security Features -->
          <div class="space-y-4">
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Minimum 8 characters</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Include letters and numbers</span>
            </div>
            <div class="flex items-center space-x-3 group">
              <div class="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300"></div>
              <span class="text-white/90 text-base">Secure encryption</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Form -->
    <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12 bg-gradient-to-br from-slate-50 to-white relative">
      <!-- Background Grid Pattern -->
      <div class="absolute inset-0 opacity-30 pointer-events-none">
        <div
          class="absolute inset-0"
          style="background-image: radial-gradient(circle at 1px 1px, rgba(5, 150, 105, 0.1) 1px, transparent 0); background-size: 40px 40px;"
        ></div>
      </div>

      <div class="max-w-md w-full space-y-8 relative z-10">
        <!-- Mobile Logo -->
        <div class="lg:hidden text-center">
          <a href="/" class="inline-block transition-all duration-300 hover:scale-105">
            <h1 class="text-3xl font-bold text-emerald-600 tracking-wide" style="font-family: 'Anton', sans-serif;">Trodoo</h1>
          </a>
        </div>

        <!-- Header -->
        <div class="text-center">
          <h2 class="text-4xl font-bold leading-tight text-slate-900" style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;">
            Reset Password
          </h2>
          <p class="mt-3 text-base leading-relaxed text-slate-600" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
            Enter your new password below
          </p>
        </div>

        <!-- Reset Form Container -->
        <div class="bg-white rounded-2xl p-8 border border-slate-200 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
          <div id="reset-form-container">
            <!-- React component will be mounted here -->
          </div>
        </div>

        <!-- Additional Links -->
        <div class="text-center space-y-3">
          <div class="text-sm text-slate-600" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
            Remember your password?
            <a href="/auth/login" class="text-emerald-600 hover:text-emerald-700 ml-1 transition-colors duration-300 font-medium">
              Sign In
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script define:vars={{ token }}>
  import { authActions } from '../../lib/state.ts';

  // Enhanced password reset form
  document.addEventListener('DOMContentLoaded', async () => {
    const container = document.getElementById('reset-form-container');
    if (!container) return;

    let isLoading = false;
    let error = '';
    let successMessage = '';
    let resetComplete = false;
    let showPassword = false;
    let showPasswordConfirm = false;

    const validatePassword = (password) => {
      const errors = [];
      if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
      }
      if (!/[a-zA-Z]/.test(password)) {
        errors.push('Password must contain at least one letter');
      }
      if (!/[0-9]/.test(password)) {
        errors.push('Password must contain at least one number');
      }
      return errors;
    };

    const renderForm = () => {
      if (resetComplete) {
        // Success state
        container.innerHTML = `
          <div class="text-center space-y-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-2" style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;">
                Password Reset Successfully!
              </h3>
              <p class="text-slate-600 mb-4" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                Your password has been updated. You can now sign in with your new password.
              </p>
            </div>

            <a
              href="/auth/login"
              class="block w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold text-base py-3 px-6 rounded-full transition-all duration-300 hover:scale-105 text-center"
              style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;"
            >
              Continue to Sign In
            </a>
          </div>
        `;
      } else {
        // Form state
        container.innerHTML = `
          <form id="reset-form" class="space-y-6">
            <div>
              <label for="password" class="block text-sm font-medium text-slate-700 mb-2" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                New Password
              </label>
              <div class="relative">
                <input
                  type="${showPassword ? 'text' : 'password'}"
                  id="password"
                  name="password"
                  required
                  class="w-full px-4 py-3 pr-12 border border-slate-300 rounded-lg bg-white text-base focus:outline-none focus:ring-2 focus:ring-emerald-600 focus:ring-opacity-20 focus:border-emerald-600 transition-all duration-300 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
                  placeholder="Enter your new password"
                  style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
                  ${isLoading ? 'disabled' : ''}
                />
                <button
                  type="button"
                  onclick="togglePasswordVisibility('password')"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 transition-colors duration-300"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    ${showPassword ? `
                      <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                    ` : `
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    `}
                  </svg>
                </button>
              </div>
            </div>

            <div>
              <label for="passwordConfirm" class="block text-sm font-medium text-slate-700 mb-2" style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;">
                Confirm New Password
              </label>
              <div class="relative">
                <input
                  type="${showPasswordConfirm ? 'text' : 'password'}"
                  id="passwordConfirm"
                  name="passwordConfirm"
                  required
                  class="w-full px-4 py-3 pr-12 border border-slate-300 rounded-lg bg-white text-base focus:outline-none focus:ring-2 focus:ring-emerald-600 focus:ring-opacity-20 focus:border-emerald-600 transition-all duration-300 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}"
                  placeholder="Confirm your new password"
                  style="font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;"
                  ${isLoading ? 'disabled' : ''}
                />
                <button
                  type="button"
                  onclick="togglePasswordVisibility('passwordConfirm')"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 transition-colors duration-300"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    ${showPasswordConfirm ? `
                      <path stroke-linecap="round" stroke-linejoin="round" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                    ` : `
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    `}
                  </svg>
                </button>
              </div>
            </div>

            ${successMessage ? `
              <div class="p-4 bg-green-50 border border-green-200 rounded-lg animate-fade-in">
                <p class="text-sm text-green-600">${successMessage}</p>
              </div>
            ` : ''}

            ${error ? `
              <div class="p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
                <p class="text-sm text-red-600">${error}</p>
              </div>
            ` : ''}

            <button
              type="submit"
              ${isLoading ? 'disabled' : ''}
              class="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold text-base py-3 px-6 rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex justify-center items-center"
              style="font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;"
            >
              ${isLoading ? `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating Password...
              ` : 'Update Password'}
            </button>
          </form>
        `;

        // Attach event listener
        const form = document.getElementById('reset-form');
        if (form) {
          form.addEventListener('submit', handleSubmit);
        }
      }
    };

    // Toggle password visibility
    window.togglePasswordVisibility = (fieldName) => {
      if (fieldName === 'password') {
        showPassword = !showPassword;
      } else if (fieldName === 'passwordConfirm') {
        showPasswordConfirm = !showPasswordConfirm;
      }
      renderForm();
    };

    const handleSubmit = async (e) => {
      e.preventDefault();

      const formData = new FormData(e.target);
      const password = formData.get('password');
      const passwordConfirm = formData.get('passwordConfirm');

      // Validation
      if (!password || !passwordConfirm) {
        error = 'Please fill in all fields.';
        successMessage = '';
        renderForm();
        return;
      }

      if (password !== passwordConfirm) {
        error = 'Passwords do not match.';
        successMessage = '';
        renderForm();
        return;
      }

      const passwordErrors = validatePassword(password);
      if (passwordErrors.length > 0) {
        error = passwordErrors.join('. ');
        successMessage = '';
        renderForm();
        return;
      }

      isLoading = true;
      error = '';
      successMessage = '';
      renderForm();

      try {
        const result = await authActions.confirmPasswordReset(token, password, passwordConfirm);

        if (result.success) {
          resetComplete = true;
          isLoading = false;
          renderForm();
        } else {
          error = result.error || 'Failed to reset password. Please try again.';
          successMessage = '';
          isLoading = false;
          renderForm();
        }
      } catch (err) {
        console.error('Password reset error:', err);
        error = 'An unexpected error occurred. Please try again.';
        successMessage = '';
        isLoading = false;
        renderForm();
      }
    };

    // Initial render
    renderForm();
  });
</script>

<style>
  /* Additional styles for the reset password page */
  .bg-pattern {
    background-image: 
      radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.1) 2px, transparent 0),
      radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.1) 2px, transparent 0);
    background-size: 100px 100px;
  }
</style>
