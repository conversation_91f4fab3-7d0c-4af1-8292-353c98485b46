---
import Layout from '@/components/core/Layout.astro';
---

<Layout 
  title="Page Not Found - Trodoo"
  description="The page you're looking for doesn't exist."
  noIndex={true}
>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <!-- 404 Illustration -->
        <div class="mx-auto h-32 w-32 text-primary-400 mb-8">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8a7.962 7.962 0 01-2 5.291z" />
          </svg>
        </div>

        <!-- Error Message -->
        <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you might have entered the wrong URL.
        </p>

        <!-- Action Buttons -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <a 
            href="/" 
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Go Home
          </a>
          
          <a 
            href="/venues" 
            class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Browse Venues
          </a>
        </div>

        <!-- Help Links -->
        <div class="mt-12 text-sm text-gray-500">
          <p class="mb-4">Need help? Try these popular pages:</p>
          <div class="space-y-2">
            <a href="/venues" class="block text-primary-600 hover:text-primary-500">Browse All Venues</a>
            <a href="/dashboard" class="block text-primary-600 hover:text-primary-500">My Dashboard</a>
            <a href="/contact" class="block text-primary-600 hover:text-primary-500">Contact Support</a>
            <a href="/help" class="block text-primary-600 hover:text-primary-500">Help Center</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Track 404 errors for analytics
  if (typeof (globalThis as any).gtag !== 'undefined') {
    (globalThis as any).gtag('event', 'page_not_found', {
      page_location: window.location.href,
      page_referrer: document.referrer
    });
  }

  // Auto-redirect common typos or old URLs
  const currentPath = window.location.pathname;
  const redirectMap: Record<string, string> = {
    '/venue': '/venues',
    '/booking': '/bookings',
    '/profile': '/dashboard/profile',
    '/login': '/auth/login',
    '/register': '/auth/register',
    '/signup': '/auth/register'
  };

  const redirectTo = redirectMap[currentPath];
  if (redirectTo) {
    setTimeout(() => {
      window.location.href = redirectTo;
    }, 3000);

    // Show redirect message
    const redirectMessage = document.createElement('div');
    redirectMessage.className = 'mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700';
    redirectMessage.innerHTML = `Redirecting to <strong>${redirectTo}</strong> in 3 seconds...`;
    const textCenter = document.querySelector('.text-center');
    if (textCenter) {
      textCenter.appendChild(redirectMessage);
    }
  }
</script>

<style>
  /* Add some subtle animations */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .text-center > * {
    animation: fadeIn 0.6s ease-out forwards;
  }
  
  .text-center > *:nth-child(2) { animation-delay: 0.1s; }
  .text-center > *:nth-child(3) { animation-delay: 0.2s; }
  .text-center > *:nth-child(4) { animation-delay: 0.3s; }
  .text-center > *:nth-child(5) { animation-delay: 0.4s; }
</style>
