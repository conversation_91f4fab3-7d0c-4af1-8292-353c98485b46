import type { APIRoute } from 'astro';
import { pb } from '../../lib/pocketbase.ts';

export const POST: APIRoute = async ({ request }) => {
  try {
    // Parse request body
    const body = await request.json();
    const { content_type, content_id, reason, reporter } = body;

    // Validate required fields
    if (!content_type || !content_id || !reason || !reporter) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields: content_type, content_id, reason, reporter' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Validate content_type
    if (!['venue', 'user_profile'].includes(content_type)) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid content_type. Must be "venue" or "user_profile"' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Validate reason length
    if (reason.length > 500) {
      return new Response(
        JSON.stringify({ 
          error: 'Reason must be 500 characters or less' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if user has already flagged this content
    try {
      const existingFlags = await pb.collection('flagged_content').getList(1, 1, {
        filter: `content_type = "${content_type}" && content_id = "${content_id}" && reporter = "${reporter}"`
      });

      if (existingFlags.items.length > 0) {
        return new Response(
          JSON.stringify({ 
            error: 'You have already flagged this content' 
          }),
          { 
            status: 409,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }
    } catch (error) {
      console.error('Error checking existing flags:', error);
      // Continue with flag creation even if check fails
    }

    // Create the flag record
    const flagData = {
      content_type,
      content_id,
      reporter,
      reason: reason.trim(),
      status: 'open'
    };

    const flag = await pb.collection('flagged_content').create(flagData);

    // Log the flag creation for monitoring
    console.log(`Content flagged: ${content_type}:${content_id} by user ${reporter}`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        flag_id: flag.id,
        message: 'Content has been flagged for review' 
      }),
      { 
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Flag content API error:', error);
    
    // Handle specific PocketBase errors
    if ((error as any).status === 400) {
      return new Response(
        JSON.stringify({
          error: 'Invalid data provided',
          details: (error as any).message
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if ((error as any).status === 403) {
      return new Response(
        JSON.stringify({ 
          error: 'Unauthorized to flag content' 
        }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: 'Failed to process flag submission' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

// Handle unsupported methods
export const GET: APIRoute = async () => {
  return new Response(
    JSON.stringify({ error: 'Method not allowed' }),
    { 
      status: 405,
      headers: { 
        'Content-Type': 'application/json',
        'Allow': 'POST'
      }
    }
  );
};

export const PUT: APIRoute = async () => {
  return new Response(
    JSON.stringify({ error: 'Method not allowed' }),
    { 
      status: 405,
      headers: { 
        'Content-Type': 'application/json',
        'Allow': 'POST'
      }
    }
  );
};

export const DELETE: APIRoute = async () => {
  return new Response(
    JSON.stringify({ error: 'Method not allowed' }),
    { 
      status: 405,
      headers: { 
        'Content-Type': 'application/json',
        'Allow': 'POST'
      }
    }
  );
};
