---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="Terms of Service - Trodoo"
  description="Read our terms of service to understand the rules and guidelines for using the Trodoo venue rental platform."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-12">
          <h1 class="text-4xl font-bold text-slate-900 mb-4">Terms of Service</h1>
          <p class="text-lg text-slate-600">
            Last updated: <span class="font-medium">January 15, 2024</span>
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <!-- Table of Contents -->
      <div class="bg-slate-50 rounded-xl p-6 mb-12">
        <h2 class="text-xl font-bold text-slate-900 mb-4">Table of Contents</h2>
        <nav class="space-y-2">
          <a href="#acceptance" class="block text-primary-600 hover:text-primary-700 transition-colors">1. Acceptance of Terms</a>
          <a href="#description" class="block text-primary-600 hover:text-primary-700 transition-colors">2. Description of Service</a>
          <a href="#user-accounts" class="block text-primary-600 hover:text-primary-700 transition-colors">3. User Accounts</a>
          <a href="#bookings" class="block text-primary-600 hover:text-primary-700 transition-colors">4. Bookings and Payments</a>
          <a href="#venue-listings" class="block text-primary-600 hover:text-primary-700 transition-colors">5. Venue Listings</a>
          <a href="#user-conduct" class="block text-primary-600 hover:text-primary-700 transition-colors">6. User Conduct</a>
          <a href="#cancellation" class="block text-primary-600 hover:text-primary-700 transition-colors">7. Cancellation Policy</a>
          <a href="#liability" class="block text-primary-600 hover:text-primary-700 transition-colors">8. Limitation of Liability</a>
          <a href="#privacy" class="block text-primary-600 hover:text-primary-700 transition-colors">9. Privacy Policy</a>
          <a href="#modifications" class="block text-primary-600 hover:text-primary-700 transition-colors">10. Modifications to Terms</a>
          <a href="#contact" class="block text-primary-600 hover:text-primary-700 transition-colors">11. Contact Information</a>
        </nav>
      </div>

      <!-- Terms Content -->
      <div class="prose prose-slate max-w-none">
        <section id="acceptance" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">1. Acceptance of Terms</h2>
          <p class="text-slate-700 mb-4">
            By accessing and using the Trodoo platform ("Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
          </p>
          <p class="text-slate-700">
            These Terms of Service ("Terms") govern your use of our website located at trodoo.com (the "Service") operated by Trodoo ("us", "we", or "our").
          </p>
        </section>

        <section id="description" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">2. Description of Service</h2>
          <p class="text-slate-700 mb-4">
            Trodoo is an online platform that connects venue owners with individuals and organizations seeking to rent spaces for events, meetings, and other activities. Our Service includes:
          </p>
          <ul class="list-disc list-inside text-slate-700 space-y-2 mb-4">
            <li>Venue listing and discovery tools</li>
            <li>Booking and reservation management</li>
            <li>Payment processing services</li>
            <li>Communication tools between renters and venue owners</li>
            <li>Review and rating systems</li>
          </ul>
        </section>

        <section id="user-accounts" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">3. User Accounts</h2>
          <p class="text-slate-700 mb-4">
            To access certain features of the Service, you must register for an account. When you create an account, you must provide information that is accurate, complete, and current at all times.
          </p>
          <p class="text-slate-700 mb-4">
            You are responsible for safeguarding the password and for all activities that occur under your account. You agree not to disclose your password to any third party.
          </p>
          <p class="text-slate-700">
            You must notify us immediately upon becoming aware of any breach of security or unauthorized use of your account.
          </p>
        </section>

        <section id="bookings" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">4. Bookings and Payments</h2>
          <p class="text-slate-700 mb-4">
            When you make a booking through our platform, you enter into a direct contractual relationship with the venue owner. Trodoo acts as an intermediary to facilitate the transaction.
          </p>
          <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
            <h4 class="font-semibold text-amber-800 mb-2">Payment Terms:</h4>
            <ul class="list-disc list-inside text-amber-700 space-y-1">
              <li>All payments are processed securely through our payment partners</li>
              <li>Payment is typically required at the time of booking</li>
              <li>Some venues may require deposits with balance due later</li>
              <li>All prices are displayed in Nigerian Naira (NGN) unless otherwise specified</li>
            </ul>
          </div>
        </section>

        <section id="venue-listings" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">5. Venue Listings</h2>
          <p class="text-slate-700 mb-4">
            Venue owners are responsible for the accuracy of their listings, including descriptions, photos, pricing, and availability. By listing a venue, you represent that:
          </p>
          <ul class="list-disc list-inside text-slate-700 space-y-2 mb-4">
            <li>You have the legal right to rent the space</li>
            <li>All information provided is accurate and up-to-date</li>
            <li>The venue meets all applicable safety and legal requirements</li>
            <li>You will honor confirmed bookings according to the terms specified</li>
          </ul>
        </section>

        <section id="user-conduct" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">6. User Conduct</h2>
          <p class="text-slate-700 mb-4">
            You agree not to use the Service to:
          </p>
          <ul class="list-disc list-inside text-slate-700 space-y-2 mb-4">
            <li>Violate any applicable laws or regulations</li>
            <li>Post false, misleading, or fraudulent content</li>
            <li>Harass, abuse, or harm other users</li>
            <li>Interfere with the proper functioning of the Service</li>
            <li>Attempt to gain unauthorized access to our systems</li>
          </ul>
        </section>

        <section id="cancellation" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">7. Cancellation Policy</h2>
          <p class="text-slate-700 mb-4">
            Cancellation policies vary by venue and are set by individual venue owners. The specific cancellation terms for your booking will be clearly displayed during the booking process and in your confirmation email.
          </p>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h4 class="font-semibold text-blue-800 mb-2">General Guidelines:</h4>
            <ul class="list-disc list-inside text-blue-700 space-y-1">
              <li>Cancellations must be made through the platform</li>
              <li>Refund amounts depend on the venue's cancellation policy</li>
              <li>Processing fees may apply to cancellations</li>
              <li>Force majeure events may affect cancellation terms</li>
            </ul>
          </div>
        </section>

        <section id="liability" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">8. Limitation of Liability</h2>
          <p class="text-slate-700 mb-4">
            Trodoo acts as an intermediary platform and is not responsible for the actual rental transactions between users and venue owners. We do not guarantee the quality, safety, or legality of venues listed on our platform.
          </p>
          <p class="text-slate-700 mb-4">
            In no event shall Trodoo be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
          </p>
        </section>

        <section id="privacy" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">9. Privacy Policy</h2>
          <p class="text-slate-700 mb-4">
            Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.
          </p>
          <a href="/privacy" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors">
            Read our Privacy Policy
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </section>

        <section id="modifications" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">10. Modifications to Terms</h2>
          <p class="text-slate-700 mb-4">
            We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.
          </p>
          <p class="text-slate-700">
            Your continued use of the Service after any such changes constitutes your acceptance of the new Terms of Service.
          </p>
        </section>

        <section id="contact" class="mb-12">
          <h2 class="text-2xl font-bold text-slate-900 mb-4">11. Contact Information</h2>
          <p class="text-slate-700 mb-4">
            If you have any questions about these Terms of Service, please contact us:
          </p>
          <div class="bg-slate-50 rounded-lg p-4">
            <p class="text-slate-700 mb-2"><strong>Email:</strong> <EMAIL></p>
            <p class="text-slate-700 mb-2"><strong>Phone:</strong> +234 (0) ************</p>
            <p class="text-slate-700"><strong>Address:</strong> Lagos, Nigeria</p>
          </div>
        </section>
      </div>
    </div>

    <!-- Footer Navigation -->
    <div class="bg-slate-50 border-t border-slate-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center">
          <h3 class="text-lg font-semibold text-slate-900 mb-6">Related Documents</h3>
          <div class="flex flex-wrap justify-center gap-6">
            <a href="/privacy" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Privacy Policy
            </a>
            <a href="/help" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Help Center
            </a>
            <a href="/contact" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Contact Us
            </a>
            <a href="/" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Back to Home
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>
</BaseLayout>

<script>
  // Smooth scrolling for table of contents links
  document.addEventListener('DOMContentLoaded', () => {
    const tocLinks = document.querySelectorAll('a[href^="#"]');
    
    tocLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        const targetId = link.getAttribute('href')?.substring(1);
        const targetElement = targetId ? document.getElementById(targetId) : null;
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          
          // Update URL without triggering page reload
          history.pushState(null, '', `#${targetId}`);
        }
      });
    });

    // Highlight current section in table of contents
    const sections = document.querySelectorAll('section[id]');
    const observerOptions = {
      rootMargin: '-20% 0px -70% 0px',
      threshold: 0
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const id = entry.target.getAttribute('id');
        const tocLink = document.querySelector(`a[href="#${id}"]`);
        
        if (tocLink) {
          if (entry.isIntersecting) {
            // Remove active class from all links
            tocLinks.forEach(link => link.classList.remove('font-bold', 'text-primary-700'));
            // Add active class to current link
            tocLink.classList.add('font-bold', 'text-primary-700');
          }
        }
      });
    }, observerOptions);

    sections.forEach(section => observer.observe(section));
  });
</script>

<style>
  /* Custom styles for terms page */
  .prose {
    max-width: none;
  }

  .prose h2 {
    scroll-margin-top: 2rem;
  }

  .prose section {
    scroll-margin-top: 2rem;
  }

  /* Table of contents active state */
  nav a.font-bold {
    background-color: rgba(5, 150, 105, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    margin: -0.25rem -0.5rem;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .max-w-4xl {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
</style>
