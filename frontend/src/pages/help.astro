---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="Help Center - Trodoo"
  description="Find answers to frequently asked questions about venue rentals, bookings, payments, and more. Get help with using the Trodoo platform."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-12 text-center">
          <h1 class="text-4xl font-bold text-slate-900 mb-4">Help Center</h1>
          <p class="text-xl text-slate-600 max-w-3xl mx-auto mb-8">
            Find answers to frequently asked questions and get help with using the Trodoo platform.
          </p>
          
          <!-- Search Bar -->
          <div class="max-w-2xl mx-auto">
            <div class="relative">
              <input
                type="text"
                id="search-input"
                placeholder="Search for help articles..."
                class="w-full px-6 py-4 pl-12 text-lg border border-slate-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              />
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-6 w-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <!-- Quick Help Categories -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hover:shadow-lg transition-shadow cursor-pointer" data-category="booking">
          <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Booking Help</h3>
          <p class="text-slate-600 text-sm">Learn how to book venues, manage reservations, and handle cancellations.</p>
        </div>

        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hover:shadow-lg transition-shadow cursor-pointer" data-category="payment">
          <div class="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Payment & Billing</h3>
          <p class="text-slate-600 text-sm">Understand payment methods, refunds, and billing processes.</p>
        </div>

        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hover:shadow-lg transition-shadow cursor-pointer" data-category="venue">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Venue Listing</h3>
          <p class="text-slate-600 text-sm">Get help with listing your venue and managing your property.</p>
        </div>

        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hover:shadow-lg transition-shadow cursor-pointer" data-category="account">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Account Management</h3>
          <p class="text-slate-600 text-sm">Manage your profile, security settings, and account preferences.</p>
        </div>

        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hover:shadow-lg transition-shadow cursor-pointer" data-category="technical">
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Technical Support</h3>
          <p class="text-slate-600 text-sm">Troubleshoot technical issues and get platform support.</p>
        </div>

        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 hover:shadow-lg transition-shadow cursor-pointer" data-category="safety">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Safety & Security</h3>
          <p class="text-slate-600 text-sm">Learn about safety measures, security features, and best practices.</p>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl font-bold text-slate-900 mb-8 text-center">Frequently Asked Questions</h2>
        
        <div id="faq-container" class="space-y-4">
          <!-- FAQ items will be populated by JavaScript -->
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-12">
          <button
            id="load-more-btn"
            class="inline-flex items-center px-6 py-3 border border-slate-300 text-base font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors"
          >
            Load More Questions
          </button>
        </div>
      </div>
    </div>

    <!-- Contact Support Section -->
    <div class="bg-gradient-to-r from-primary-50 to-secondary-50 border-t border-primary-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h3 class="text-2xl font-bold text-slate-900 mb-4">Still need help?</h3>
          <p class="text-slate-600 mb-8 max-w-2xl mx-auto">
            Can't find what you're looking for? Our support team is here to help you with any questions or issues.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              class="inline-flex items-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
            >
              Contact Support
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
            <a
              href="mailto:<EMAIL>"
              class="inline-flex items-center px-6 py-3 border border-slate-300 text-slate-700 font-medium rounded-lg bg-white hover:bg-slate-50 transition-colors"
            >
              Email Us
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>
</BaseLayout>

<script>
  // FAQ data
  const faqData = [
    {
      category: 'booking',
      question: 'How do I book a venue?',
      answer: 'To book a venue, browse our listings, select your preferred venue, choose your dates, and complete the booking process with payment. You\'ll receive a confirmation email once your booking is confirmed.'
    },
    {
      category: 'booking',
      question: 'Can I cancel my booking?',
      answer: 'Yes, you can cancel your booking according to the venue\'s cancellation policy. Check the specific cancellation terms on your booking confirmation or in the venue details.'
    },
    {
      category: 'payment',
      question: 'What payment methods do you accept?',
      answer: 'We accept major credit cards, debit cards, and bank transfers. All payments are processed securely through our payment partners.'
    },
    {
      category: 'payment',
      question: 'When will I be charged?',
      answer: 'Payment is typically processed immediately upon booking confirmation. Some venues may require a deposit with the balance due closer to your event date.'
    },
    {
      category: 'venue',
      question: 'How do I list my venue?',
      answer: 'To list your venue, create an account, complete your profile, and use our venue listing form to add photos, descriptions, pricing, and availability.'
    },
    {
      category: 'venue',
      question: 'What fees do venue owners pay?',
      answer: 'Venue owners pay a small commission on successful bookings. There are no upfront fees to list your venue on our platform.'
    },
    {
      category: 'account',
      question: 'How do I reset my password?',
      answer: 'Click "Forgot Password" on the login page, enter your email address, and follow the instructions in the reset email we send you.'
    },
    {
      category: 'account',
      question: 'Can I change my email address?',
      answer: 'Yes, you can update your email address in your account settings. You\'ll need to verify the new email address before the change takes effect.'
    },
    {
      category: 'technical',
      question: 'The website is not loading properly',
      answer: 'Try clearing your browser cache, disabling browser extensions, or using a different browser. If the issue persists, contact our technical support team.'
    },
    {
      category: 'safety',
      question: 'How do you verify venues?',
      answer: 'We verify venue listings through documentation review, photo verification, and in some cases, physical inspections to ensure accuracy and safety.'
    }
  ];

  let displayedFAQs = 0;
  const faqsPerLoad = 5;
  let currentFilter = 'all';

  // Initialize help page
  document.addEventListener('DOMContentLoaded', () => {
    loadFAQs();
    initializeSearch();
    initializeCategoryFilters();
    initializeLoadMore();
  });

  // Load FAQ items
  function loadFAQs(filter = 'all') {
    const container = document.getElementById('faq-container');
    if (!container) return;

    let filteredFAQs = filter === 'all' ? faqData : faqData.filter(faq => faq.category === filter);
    
    if (filter !== currentFilter) {
      displayedFAQs = 0;
      container.innerHTML = '';
      currentFilter = filter;
    }

    const faqsToShow = filteredFAQs.slice(displayedFAQs, displayedFAQs + faqsPerLoad);
    
    faqsToShow.forEach((faq, index) => {
      const faqElement = createFAQElement(faq, displayedFAQs + index);
      container.appendChild(faqElement);
    });

    displayedFAQs += faqsToShow.length;

    // Update load more button
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
      loadMoreBtn.style.display = displayedFAQs >= filteredFAQs.length ? 'none' : 'inline-flex';
    }
  }

  // Create FAQ element
  function createFAQElement(faq: any, index: number) {
    const div = document.createElement('div');
    div.className = 'bg-white rounded-lg border border-slate-200 shadow-sm';
    div.innerHTML = `
      <button
        class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-slate-50 transition-colors"
        onclick="toggleFAQ(${index})"
      >
        <span class="font-medium text-slate-900">${faq.question}</span>
        <svg class="w-5 h-5 text-slate-500 transform transition-transform" id="icon-${index}">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div class="hidden px-6 pb-4" id="answer-${index}">
        <p class="text-slate-600">${faq.answer}</p>
      </div>
    `;
    return div;
  }

  // Toggle FAQ answer
  function toggleFAQ(index: number) {
    const answer = document.getElementById(`answer-${index}`);
    const icon = document.getElementById(`icon-${index}`);
    
    if (answer && icon) {
      const isHidden = answer.classList.contains('hidden');
      
      if (isHidden) {
        answer.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
      } else {
        answer.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
      }
    }
  }

  // Initialize search functionality
  function initializeSearch() {
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    if (!searchInput) return;

    searchInput.addEventListener('input', (e) => {
      const query = (e.target as HTMLInputElement).value.toLowerCase();
      filterFAQsBySearch(query);
    });
  }

  // Filter FAQs by search query
  function filterFAQsBySearch(query: string) {
    const container = document.getElementById('faq-container');
    if (!container) return;

    if (query.length === 0) {
      loadFAQs(currentFilter);
      return;
    }

    const filteredFAQs = faqData.filter(faq => 
      faq.question.toLowerCase().includes(query) || 
      faq.answer.toLowerCase().includes(query)
    );

    container.innerHTML = '';
    filteredFAQs.forEach((faq, index) => {
      const faqElement = createFAQElement(faq, index);
      container.appendChild(faqElement);
    });

    // Hide load more button during search
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
      loadMoreBtn.style.display = 'none';
    }
  }

  // Initialize category filters
  function initializeCategoryFilters() {
    const categoryCards = document.querySelectorAll('[data-category]');
    categoryCards.forEach(card => {
      card.addEventListener('click', (e) => {
        const category = (e.currentTarget as HTMLElement).dataset.category;
        if (category) {
          loadFAQs(category);
          
          // Clear search
          const searchInput = document.getElementById('search-input') as HTMLInputElement;
          if (searchInput) searchInput.value = '';
        }
      });
    });
  }

  // Initialize load more functionality
  function initializeLoadMore() {
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
      loadMoreBtn.addEventListener('click', () => {
        loadFAQs(currentFilter);
      });
    }
  }

  // Make toggleFAQ available globally
  (globalThis as any).toggleFAQ = toggleFAQ;
</script>

<style>
  /* Custom styles for help page */
  .shadow-card {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  /* Search input focus styles */
  #search-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  /* Category card hover effects */
  [data-category]:hover {
    transform: translateY(-2px);
  }

  /* FAQ transition effects */
  .transform {
    transition: transform 0.2s ease-in-out;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .max-w-7xl {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
      gap: 1rem;
    }
  }
</style>
