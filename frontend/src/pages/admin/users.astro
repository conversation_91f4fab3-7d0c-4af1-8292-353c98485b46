---
import AdminLayout from '../../layouts/AdminLayout.astro';
---

<AdminLayout title="User Management - Admin Dashboard">
  <div class="space-y-8">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-[#1F2937]">User Management</h1>
        <p class="mt-2 text-[#6B7280]">
          Manage user accounts, roles, and permissions across the platform
        </p>
      </div>
      
      <div class="flex space-x-4">
        <a 
          href="/admin" 
          class="inline-flex items-center px-4 py-2 border-2 border-[#E5E7EB] text-sm font-medium rounded-lg text-[#6B7280] bg-white hover:bg-[#F9FAFB] transition-all duration-300"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Dashboard
        </a>
        
        <a 
          href="/admin/flagged-content" 
          class="inline-flex items-center px-4 py-2 bg-[#F59E0B] text-[#1F2937] text-sm font-medium rounded-lg hover:bg-[#D97706] transition-colors duration-300"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          Review Flags
        </a>
      </div>
    </div>

    <!-- User Management Table -->
    <div id="user-management-container">
      <!-- Loading State -->
      <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB] p-6">
        <div class="animate-pulse space-y-4">
          <div class="flex justify-between items-center">
            <div class="h-6 bg-[#E5E7EB] rounded w-48"></div>
            <div class="flex space-x-4">
              <div class="h-10 bg-[#E5E7EB] rounded w-64"></div>
              <div class="h-10 bg-[#E5E7EB] rounded w-32"></div>
              <div class="h-10 bg-[#E5E7EB] rounded w-32"></div>
            </div>
          </div>
          <div class="space-y-3">
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
            <div class="h-16 bg-[#E5E7EB] rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</AdminLayout>

<script>
  // This would normally import and render the React UserManagementTable component
  // For now, we'll show a placeholder with the table structure
  
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('user-management-container');
    if (!container) return;

    // Simulate loading time
    setTimeout(() => {
      container.innerHTML = `
        <div class="bg-white rounded-xl shadow-lg border border-[#E5E7EB]">
          <!-- Header -->
          <div class="p-6 border-b border-[#E5E7EB]">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h3 class="text-lg font-bold text-[#1F2937]">User Management</h3>
                <p class="text-sm text-[#6B7280]">
                  Manage user accounts, roles, and permissions
                </p>
              </div>
              
              <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <!-- Search -->
                <div class="relative">
                  <input
                    type="text"
                    placeholder="Search users..."
                    class="w-full sm:w-64 px-4 py-2 pl-10 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]"
                  />
                  <svg class="absolute left-3 top-2.5 h-4 w-4 text-[#6B7280]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
                
                <!-- Role Filter -->
                <select class="px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]">
                  <option value="all">All Roles</option>
                  <option value="renter">Renter</option>
                  <option value="owner">Owner</option>
                  <option value="admin">Admin</option>
                </select>
                
                <!-- Status Filter -->
                <select class="px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#059669] focus:border-[#059669]">
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Table -->
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-[#F9FAFB] border-b border-[#E5E7EB]">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    User
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Roles
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Joined
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-[#6B7280] uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-[#E5E7EB]">
                <tr class="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-[#059669] rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">J</span>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-[#1F2937]">John Doe</div>
                        <div class="text-sm text-[#6B7280]"><EMAIL></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">
                        Renter
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                      Active
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    Jan 10, 2024
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button class="text-[#059669] hover:text-[#047857] transition-colors duration-300" title="View User">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                      <button class="text-[#6B7280] hover:text-[#1F2937] transition-colors duration-300" title="Edit User">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>

                <tr class="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-[#059669] rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">J</span>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-[#1F2937]">Jane Smith</div>
                        <div class="text-sm text-[#6B7280]"><EMAIL></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full font-medium">
                        Renter
                      </span>
                      <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                        Owner
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                      Active
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    Jan 8, 2024
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button class="text-[#059669] hover:text-[#047857] transition-colors duration-300" title="View User">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                      <button class="text-[#6B7280] hover:text-[#1F2937] transition-colors duration-300" title="Edit User">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>

                <tr class="hover:bg-[#F9FAFB] transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-[#059669] rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">M</span>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-[#1F2937]">Mike Johnson</div>
                        <div class="text-sm text-[#6B7280]"><EMAIL></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <span class="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                        Admin
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full font-medium">
                      Active
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-[#6B7280]">
                    Jan 1, 2024
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button class="text-[#059669] hover:text-[#047857] transition-colors duration-300" title="View User">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                      <button class="text-[#6B7280] hover:text-[#1F2937] transition-colors duration-300" title="Edit User">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Footer -->
          <div class="px-6 py-4 border-t border-[#E5E7EB] bg-[#F9FAFB]">
            <div class="flex items-center justify-between">
              <p class="text-sm text-[#6B7280]">
                Showing 3 of 1,247 users
              </p>
              <button class="text-sm text-[#059669] hover:text-[#047857] font-medium transition-colors duration-300">
                Refresh
              </button>
            </div>
          </div>
        </div>
      `;
    }, 1500);
  });
</script>

<style>
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
</style>
