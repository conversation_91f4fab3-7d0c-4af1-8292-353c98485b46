---
import BaseLayout from '../../layouts/BaseLayout.astro';

// Check if user is authenticated
const authCookie = Astro.cookies.get('pb_auth');
if (!authCookie) {
  return Astro.redirect('/auth/login');
}
---

<BaseLayout
  title="My Venues - Trodoo"
  description="Manage your venue listings, view bookings, and track performance on the Trodoo platform."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- App Wrapper Container -->
    <div id="app-wrapper-container">
      <!-- Header -->
      <div class="bg-white border-b border-slate-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="py-8">
            <div class="flex items-center justify-between">
              <div>
                <nav class="flex items-center space-x-2 text-sm text-slate-500 mb-2">
                  <a href="/dashboard" class="hover:text-slate-700 transition-colors">Dashboard</a>
                  <span>/</span>
                  <span class="text-slate-900 font-medium">My Venues</span>
                </nav>
                <h1 class="text-3xl font-bold text-slate-900">My Venues</h1>
                <p class="mt-2 text-slate-600">
                  Manage your venue listings and track their performance.
                </p>
              </div>
              <div class="flex items-center space-x-3">
                <a
                  href="/venues/new"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add New Venue
                </a>
                <a
                  href="/dashboard"
                  class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  ← Back to Dashboard
                </a>

                <!-- Profile Dropdown -->
                <div id="profile-dropdown-container"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Overview -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Venues -->
          <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-slate-600">Total Venues</p>
                <p class="text-2xl font-bold text-slate-900" id="total-venues">-</p>
              </div>
            </div>
          </div>

          <!-- Active Bookings -->
          <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-slate-600">Active Bookings</p>
                <p class="text-2xl font-bold text-slate-900" id="active-bookings">-</p>
              </div>
            </div>
          </div>

          <!-- Monthly Revenue -->
          <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-slate-600">This Month</p>
                <p class="text-2xl font-bold text-slate-900" id="monthly-revenue">₦0</p>
              </div>
            </div>
          </div>

          <!-- Average Rating -->
          <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-slate-600">Average Rating</p>
                <p class="text-2xl font-bold text-slate-900" id="average-rating">-</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Filter Tabs -->
        <div class="mb-8">
          <div class="border-b border-slate-200">
            <nav class="-mb-px flex space-x-8" id="venue-tabs">
              <button
                class="tab-button active border-transparent text-primary-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="all"
              >
                All Venues
              </button>
              <button
                class="tab-button border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="active"
              >
                Active
              </button>
              <button
                class="tab-button border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="draft"
              >
                Draft
              </button>
              <button
                class="tab-button border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="inactive"
              >
                Inactive
              </button>
            </nav>
          </div>
        </div>

        <!-- Venues Container -->
        <div id="venues-container">
          <!-- Loading State -->
          <div id="loading-state" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 animate-pulse">
              <div class="w-full h-48 bg-slate-200 rounded-lg mb-4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-slate-200 rounded w-3/4"></div>
                <div class="h-3 bg-slate-200 rounded w-1/2"></div>
                <div class="h-3 bg-slate-200 rounded w-1/4"></div>
              </div>
            </div>
            <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 animate-pulse">
              <div class="w-full h-48 bg-slate-200 rounded-lg mb-4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-slate-200 rounded w-3/4"></div>
                <div class="h-3 bg-slate-200 rounded w-1/2"></div>
                <div class="h-3 bg-slate-200 rounded w-1/4"></div>
              </div>
            </div>
            <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6 animate-pulse">
              <div class="w-full h-48 bg-slate-200 rounded-lg mb-4"></div>
              <div class="space-y-2">
                <div class="h-4 bg-slate-200 rounded w-3/4"></div>
                <div class="h-3 bg-slate-200 rounded w-1/2"></div>
                <div class="h-3 bg-slate-200 rounded w-1/4"></div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div id="empty-state" class="hidden text-center py-16">
            <div class="w-24 h-24 text-slate-400 mx-auto mb-6">
              <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 mb-4">No venues found</h3>
            <p class="text-slate-600 mb-8 max-w-md mx-auto">
              You haven't listed any venues yet. Start earning by adding your first venue to the platform.
            </p>
            <a
              href="/venues/new"
              class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              List Your First Venue
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Dock Navigation -->
    <div id="header-dock-container"></div>
  </main>
</BaseLayout>

<!-- React Components Integration -->
<script>
  import HeaderDock from '../../components/HeaderDock.tsx';
  import AppWrapper from '../../components/common/AppWrapper.tsx';
  import ProfileDropdown from '../../components/common/ProfileDropdown.tsx';
  import { createRoot } from 'react-dom/client';
  import React from 'react';
  import { userStore, initializeAuth, authLoadingStore } from '../../lib/state.ts';

  // Helper function to wait for authentication to complete
  function waitForAuth(): Promise<void> {
    return new Promise((resolve) => {
      const checkAuth = () => {
        if (!authLoadingStore.get()) {
          resolve();
        } else {
          setTimeout(checkAuth, 100);
        }
      };
      checkAuth();
    });
  }

  // My Venues page initialization
  document.addEventListener('DOMContentLoaded', async () => {
    // Initialize authentication state first
    initializeAuth();

    // Wait for auth to be ready
    await waitForAuth();

    // Mount AppWrapper component to wrap the entire content
    const appWrapperContainer = document.getElementById('app-wrapper-container');
    if (appWrapperContainer) {
      const originalContent = appWrapperContainer.innerHTML;
      const root = createRoot(appWrapperContainer);
      root.render(React.createElement(AppWrapper, {
        children: React.createElement('div', { dangerouslySetInnerHTML: { __html: originalContent } })
      }));

      // Wait for React to render the new DOM, then mount ProfileDropdown
      setTimeout(() => {
        const profileDropdownContainer = document.getElementById('profile-dropdown-container');
        if (profileDropdownContainer) {
          const profileRoot = createRoot(profileDropdownContainer);
          profileRoot.render(React.createElement(ProfileDropdown));
        }
      }, 100);
    }

    // Mount HeaderDock component
    const headerDockContainer = document.getElementById('header-dock-container');
    if (headerDockContainer) {
      const root = createRoot(headerDockContainer);
      root.render(React.createElement(HeaderDock));
    }

    // Get current user from state
    const user = userStore.get();
    if (!user) {
      console.error('No authenticated user found');
      globalThis.location.href = '/auth/login';
      return;
    }

    // Initialize tab functionality
    initializeTabs();

    // Load venues data
    await loadVenuesData();
  });

  // Tab functionality
  function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.target as HTMLButtonElement;
        const tabType = target.dataset.tab;
        
        // Update active tab
        tabButtons.forEach(btn => {
          btn.classList.remove('active', 'border-primary-600', 'text-primary-600');
          btn.classList.add('border-transparent', 'text-slate-500');
        });
        
        target.classList.add('active', 'border-primary-600', 'text-primary-600');
        target.classList.remove('border-transparent', 'text-slate-500');
        
        // Filter venues
        filterVenues(tabType);
      });
    });
  }

  let allVenues: any[] = [];

  // Load venues data
  async function loadVenuesData() {
    try {
      const user = userStore.get();
      if (!user) return;

      // Simulate API call to load user's venues
      // Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data for demonstration
      allVenues = [
        {
          id: '1',
          title: 'Modern Conference Room',
          status: 'active',
          price: 50000,
          bookings: 12,
          rating: 4.8,
          image: '/images/venue-placeholder.jpg'
        },
        {
          id: '2',
          title: 'Elegant Event Hall',
          status: 'active',
          price: 150000,
          bookings: 8,
          rating: 4.9,
          image: '/images/venue-placeholder.jpg'
        }
      ];

      // Update stats
      updateStats();

      // Hide loading state
      const loadingState = document.getElementById('loading-state');
      if (loadingState) loadingState.style.display = 'none';

      // Show venues or empty state
      if (allVenues.length > 0) {
        renderVenues(allVenues);
      } else {
        showEmptyState();
      }

    } catch (error) {
      console.error('Failed to load venues:', error);
      showErrorState();
    }
  }

  // Update stats display
  function updateStats() {
    const totalVenues = allVenues.length;
    const activeBookings = allVenues.reduce((sum, venue) => sum + venue.bookings, 0);
    const monthlyRevenue = allVenues.reduce((sum, venue) => sum + (venue.price * venue.bookings), 0);
    const averageRating = totalVenues > 0 ? 
      (allVenues.reduce((sum, venue) => sum + venue.rating, 0) / totalVenues).toFixed(1) : 0;

    document.getElementById('total-venues')!.textContent = totalVenues.toString();
    document.getElementById('active-bookings')!.textContent = activeBookings.toString();
    document.getElementById('monthly-revenue')!.textContent = `₦${monthlyRevenue.toLocaleString()}`;
    document.getElementById('average-rating')!.textContent = averageRating.toString();
  }

  // Filter venues by type
  function filterVenues(type: string) {
    let filteredVenues = allVenues;

    switch (type) {
      case 'active':
        filteredVenues = allVenues.filter(venue => venue.status === 'active');
        break;
      case 'draft':
        filteredVenues = allVenues.filter(venue => venue.status === 'draft');
        break;
      case 'inactive':
        filteredVenues = allVenues.filter(venue => venue.status === 'inactive');
        break;
      default:
        filteredVenues = allVenues;
    }

    if (filteredVenues.length > 0) {
      renderVenues(filteredVenues);
    } else {
      showEmptyState();
    }
  }

  // Render venues grid
  function renderVenues(venues: any[]) {
    const container = document.getElementById('venues-container');
    if (!container) return;

    container.innerHTML = `
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        ${venues.map(venue => `
          <div class="bg-white rounded-xl shadow-card border border-slate-200 overflow-hidden hover:shadow-lg transition-shadow">
            <div class="aspect-w-16 aspect-h-9">
              <img src="${venue.image}" alt="${venue.title}" class="w-full h-48 object-cover" />
            </div>
            <div class="p-6">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-slate-900">${venue.title}</h3>
                <span class="px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(venue.status)}">
                  ${venue.status.charAt(0).toUpperCase() + venue.status.slice(1)}
                </span>
              </div>
              <p class="text-slate-600 text-sm mb-4">₦${venue.price.toLocaleString()} per day</p>
              <div class="flex items-center justify-between text-sm text-slate-500 mb-4">
                <span>${venue.bookings} bookings</span>
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                  ${venue.rating}
                </div>
              </div>
              <div class="flex space-x-2">
                <a href="/venues/${venue.id}/edit" class="flex-1 text-center px-3 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors">
                  Edit
                </a>
                <a href="/venues/${venue.id}" class="flex-1 text-center px-3 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                  View
                </a>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  // Get status badge CSS class
  function getStatusBadgeClass(status: string) {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-slate-100 text-slate-800';
    }
  }

  // Show empty state
  function showEmptyState() {
    const container = document.getElementById('venues-container');
    const emptyState = document.getElementById('empty-state');
    if (container && emptyState) {
      container.innerHTML = '';
      container.appendChild(emptyState.cloneNode(true));
      container.lastElementChild?.classList.remove('hidden');
    }
  }

  // Show error state
  function showErrorState() {
    const container = document.getElementById('venues-container');
    if (container) {
      container.innerHTML = `
        <div class="text-center py-16">
          <div class="w-24 h-24 text-red-500 mx-auto mb-6">
            <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-slate-900 mb-4">Failed to load venues</h3>
          <p class="text-slate-600 mb-8">There was an error loading your venues. Please try again.</p>
          <button onclick="globalThis.location.reload()" class="inline-flex items-center px-6 py-3 border border-slate-300 text-base font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors">
            Try Again
          </button>
        </div>
      `;
    }
  }
</script>

<style>
  /* Custom styles for my venues page */
  .shadow-card {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  /* Tab button active state */
  .tab-button.active {
    border-color: var(--color-primary-600);
    color: var(--color-primary-600);
  }

  /* Loading animation */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  /* Aspect ratio for venue images */
  .aspect-w-16 {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
  }

  .aspect-w-16 img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .max-w-7xl {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
    
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    #venue-tabs {
      flex-wrap: wrap;
      gap: 1rem;
    }
    
    .tab-button {
      flex: 1;
      text-align: center;
    }
  }
</style>
