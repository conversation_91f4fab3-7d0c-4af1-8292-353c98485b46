---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="How It Works - Trodoo"
  description="Learn how <PERSON><PERSON><PERSON> connects venue owners with renters. Discover the simple process for listing venues and booking spaces for your events."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">How Trodoo Works</h1>
          <p class="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto">
            Connecting venue owners with renters through a simple, secure, and efficient platform
          </p>
        </div>
      </div>
    </div>

    <!-- User Type Selection -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-slate-900 mb-4">Choose Your Journey</h2>
        <p class="text-lg text-slate-600 max-w-2xl mx-auto">
          Whether you're looking to rent a space or list your venue, we've made the process simple and straightforward.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <!-- For Renters -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-8 hover:shadow-lg transition-shadow">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-2">Looking for a Venue?</h3>
            <p class="text-slate-600">Find the perfect space for your event, meeting, or special occasion.</p>
          </div>
          <button
            onclick="showRenterSteps()"
            class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors"
          >
            See How to Book
          </button>
        </div>

        <!-- For Venue Owners -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-8 hover:shadow-lg transition-shadow">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-2">Have a Venue to List?</h3>
            <p class="text-slate-600">Start earning by listing your space and connecting with potential renters.</p>
          </div>
          <button
            onclick="showOwnerSteps()"
            class="w-full bg-secondary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-secondary-700 transition-colors"
          >
            See How to List
          </button>
        </div>
      </div>
    </div>

    <!-- Steps for Renters -->
    <div id="renter-steps" class="hidden bg-gradient-to-r from-primary-50 to-primary-100 border-y border-primary-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-slate-900 mb-4">How to Book a Venue</h2>
          <p class="text-lg text-slate-600">Simple steps to find and book your perfect space</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Step 1 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">1</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Search & Browse</h3>
            <p class="text-slate-600 text-sm">Browse our curated selection of venues or use filters to find exactly what you need.</p>
          </div>

          <!-- Step 2 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">2</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">View Details</h3>
            <p class="text-slate-600 text-sm">Check photos, amenities, pricing, and availability. Read reviews from previous renters.</p>
          </div>

          <!-- Step 3 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">3</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Book & Pay</h3>
            <p class="text-slate-600 text-sm">Select your dates, complete the booking form, and make secure payment through our platform.</p>
          </div>

          <!-- Step 4 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">4</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Enjoy Your Event</h3>
            <p class="text-slate-600 text-sm">Receive confirmation details and enjoy your event. Leave a review to help future renters.</p>
          </div>
        </div>

        <div class="text-center mt-12">
          <a
            href="/venues"
            class="inline-flex items-center px-8 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
          >
            Start Browsing Venues
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>

    <!-- Steps for Venue Owners -->
    <div id="owner-steps" class="hidden bg-gradient-to-r from-secondary-50 to-secondary-100 border-y border-secondary-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-slate-900 mb-4">How to List Your Venue</h2>
          <p class="text-lg text-slate-600">Start earning from your space in just a few steps</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Step 1 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-secondary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">1</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Create Account</h3>
            <p class="text-slate-600 text-sm">Sign up for free and complete your profile with basic information about yourself.</p>
          </div>

          <!-- Step 2 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-secondary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">2</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Add Your Venue</h3>
            <p class="text-slate-600 text-sm">Upload photos, write descriptions, set pricing, and specify amenities and availability.</p>
          </div>

          <!-- Step 3 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-secondary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">3</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Get Bookings</h3>
            <p class="text-slate-600 text-sm">Receive booking requests, communicate with renters, and manage your calendar.</p>
          </div>

          <!-- Step 4 -->
          <div class="text-center">
            <div class="w-12 h-12 bg-secondary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">4</div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Earn Money</h3>
            <p class="text-slate-600 text-sm">Get paid securely through our platform and build your reputation with positive reviews.</p>
          </div>
        </div>

        <div class="text-center mt-12">
          <a
            href="/venues/new"
            class="inline-flex items-center px-8 py-3 bg-secondary-600 text-white font-medium rounded-lg hover:bg-secondary-700 transition-colors"
          >
            List Your Venue
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>

    <!-- Key Features -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-slate-900 mb-4">Why Choose Trodoo?</h2>
        <p class="text-lg text-slate-600 max-w-2xl mx-auto">
          We've built our platform with both venue owners and renters in mind, ensuring a smooth experience for everyone.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Secure Payments -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Secure Payments</h3>
          <p class="text-slate-600 text-sm">All transactions are processed securely with industry-standard encryption and fraud protection.</p>
        </div>

        <!-- 24/7 Support -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">24/7 Support</h3>
          <p class="text-slate-600 text-sm">Our dedicated support team is available around the clock to help with any questions or issues.</p>
        </div>

        <!-- Verified Listings -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Verified Listings</h3>
          <p class="text-slate-600 text-sm">All venues are verified for accuracy and quality to ensure you get exactly what you expect.</p>
        </div>

        <!-- Easy Communication -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Easy Communication</h3>
          <p class="text-slate-600 text-sm">Built-in messaging system allows seamless communication between venue owners and renters.</p>
        </div>

        <!-- Flexible Booking -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Flexible Booking</h3>
          <p class="text-slate-600 text-sm">Book for hours, days, or longer periods with flexible cancellation policies set by venue owners.</p>
        </div>

        <!-- Reviews & Ratings -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-6">
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-slate-900 mb-2">Reviews & Ratings</h3>
          <p class="text-slate-600 text-sm">Transparent review system helps you make informed decisions and builds trust in our community.</p>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-gradient-to-r from-slate-900 to-slate-800 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p class="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Join thousands of venue owners and renters who trust Trodoo for their space needs.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/venues"
              class="inline-flex items-center px-8 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
            >
              Find a Venue
            </a>
            <a
              href="/venues/new"
              class="inline-flex items-center px-8 py-3 border border-white text-white font-medium rounded-lg hover:bg-white hover:text-slate-900 transition-colors"
            >
              List Your Space
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-slate-900 mb-4">Frequently Asked Questions</h2>
        <p class="text-lg text-slate-600">Quick answers to common questions about using Trodoo</p>
      </div>

      <div class="max-w-3xl mx-auto space-y-4">
        <div class="bg-white rounded-lg border border-slate-200 shadow-sm">
          <button
            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-slate-50 transition-colors"
            onclick="toggleFAQ(0)"
          >
            <span class="font-medium text-slate-900">How much does it cost to use Trodoo?</span>
            <svg class="w-5 h-5 text-slate-500 transform transition-transform" id="icon-0">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" id="answer-0">
            <p class="text-slate-600">It's free to browse and list venues. We only charge a small service fee on successful bookings to keep the platform running and secure.</p>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-slate-200 shadow-sm">
          <button
            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-slate-50 transition-colors"
            onclick="toggleFAQ(1)"
          >
            <span class="font-medium text-slate-900">How do I know if a venue is legitimate?</span>
            <svg class="w-5 h-5 text-slate-500 transform transition-transform" id="icon-1">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" id="answer-1">
            <p class="text-slate-600">All venues go through our verification process. We check documentation, photos, and in some cases conduct physical inspections. Look for the verified badge on listings.</p>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-slate-200 shadow-sm">
          <button
            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-slate-50 transition-colors"
            onclick="toggleFAQ(2)"
          >
            <span class="font-medium text-slate-900">What if I need to cancel my booking?</span>
            <svg class="w-5 h-5 text-slate-500 transform transition-transform" id="icon-2">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" id="answer-2">
            <p class="text-slate-600">Cancellation policies vary by venue owner. Check the specific policy before booking. Most venues offer flexible cancellation with advance notice.</p>
          </div>
        </div>
      </div>

      <div class="text-center mt-12">
        <a
          href="/help"
          class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors"
        >
          View All FAQs
          <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>
    </div>
  </main>
</BaseLayout>

<script>
  // Show/hide steps based on user selection
  function showRenterSteps() {
    const renterSteps = document.getElementById('renter-steps');
    const ownerSteps = document.getElementById('owner-steps');
    
    if (renterSteps && ownerSteps) {
      renterSteps.classList.remove('hidden');
      ownerSteps.classList.add('hidden');
      
      // Smooth scroll to the steps section
      renterSteps.scrollIntoView({ behavior: 'smooth' });
    }
  }

  function showOwnerSteps() {
    const renterSteps = document.getElementById('renter-steps');
    const ownerSteps = document.getElementById('owner-steps');
    
    if (renterSteps && ownerSteps) {
      ownerSteps.classList.remove('hidden');
      renterSteps.classList.add('hidden');
      
      // Smooth scroll to the steps section
      ownerSteps.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // FAQ toggle functionality
  function toggleFAQ(index: number) {
    const answer = document.getElementById(`answer-${index}`);
    const icon = document.getElementById(`icon-${index}`);
    
    if (answer && icon) {
      const isHidden = answer.classList.contains('hidden');
      
      if (isHidden) {
        answer.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
      } else {
        answer.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
      }
    }
  }

  // Make functions available globally
  (globalThis as any).showRenterSteps = showRenterSteps;
  (globalThis as any).showOwnerSteps = showOwnerSteps;
  (globalThis as any).toggleFAQ = toggleFAQ;

  // Auto-show steps based on URL hash
  document.addEventListener('DOMContentLoaded', () => {
    const hash = window.location.hash;
    if (hash === '#renters') {
      showRenterSteps();
    } else if (hash === '#owners') {
      showOwnerSteps();
    }
  });
</script>

<style>
  /* Custom styles for how-it-works page */
  .shadow-card {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  /* Step number styling */
  .step-number {
    background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  }

  /* FAQ transition effects */
  .transform {
    transition: transform 0.2s ease-in-out;
  }

  /* Hover effects for cards */
  .hover\\:shadow-lg:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .max-w-7xl {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
    
    .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .text-4xl.md\\:text-5xl {
      font-size: 2.5rem;
    }
    
    .text-xl.md\\:text-2xl {
      font-size: 1.25rem;
    }
  }
</style>
