---
import { getCollection, type CollectionEntry } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import Header from '../../components/core/Header.astro';
import Footer from '@/components/Footer/Footer.tsx';
import GradientText from '../../TextAnimations/GradientText/GradientText.tsx';
import TrueFocus from '../../TextAnimations/TrueFocus/TrueFocus.tsx';

// Get all blog posts
const posts = await getCollection('blog');

// Sort posts by publication date (newest first)
const sortedPosts = posts.sort(
  (a: CollectionEntry<'blog'>, b: CollectionEntry<'blog'>) =>
    new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime(),
);
---

<BaseLayout 
  title="Blog - Trodoo"
  description="Stay updated with the latest news, tips, and insights about venue rentals and event planning from the Trodoo team."
>
  <Header />
  
  <main>
    <!-- Hero Section -->
    <section class="bg-secondary-50 py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <GradientText
            client:load
            className="text-4xl md:text-5xl font-bold text-secondary-900 mb-4"
          >
            Our Blog
          </GradientText>
          <p class="text-xl text-secondary-600 max-w-2xl mx-auto">
            Discover insights, tips, and stories about venue rentals, event planning, and the future of the sharing economy.
          </p>
        </div>
      </div>
    </section>

    <!-- Blog Posts -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {sortedPosts.length > 0 ? (
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sortedPosts.map((post: CollectionEntry<'blog'>) => (
              <article class="card overflow-hidden hover:shadow-lg transition-shadow duration-300">
                {post.data.heroImage && (
                  <div class="aspect-video bg-secondary-200">
                    <img 
                      src={post.data.heroImage} 
                      alt={post.data.title}
                      class="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                )}
                
                <div class="p-6">
                  <div class="flex items-center text-sm text-secondary-500 mb-3">
                    <time datetime={post.data.pubDate.toISOString()}>
                      {post.data.pubDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </time>
                    <span class="mx-2">•</span>
                    <span>{post.data.author}</span>
                  </div>
                  
                  <h2 class="text-xl font-semibold text-secondary-900 mb-3 line-clamp-2">
                    <a href={`/blog/${post.slug}`} class="hover:text-primary-600 transition-colors">
                      {post.data.title}
                    </a>
                  </h2>
                  
                  <p class="text-secondary-600 mb-4 line-clamp-3">
                    {post.data.description}
                  </p>
                  
                  <a 
                    href={`/blog/${post.slug}`}
                    class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors"
                  >
                    Read more
                    <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </a>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div class="text-center py-16">
            <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-semibold text-secondary-900 mb-2">No posts yet</h2>
            <p class="text-secondary-600">Check back soon for our latest insights and updates!</p>
          </div>
        )}
      </div>
    </section>
  </main>
  
  <Footer />
</BaseLayout>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
