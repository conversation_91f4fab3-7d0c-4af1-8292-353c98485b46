---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="Contact Us - Trodoo"
  description="Get in touch with our support team. We're here to help with any questions about venue rentals, bookings, or platform features."
>
  <main class="min-h-screen bg-gradient-to-br from-slate-50 to-white">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-8">
          <div class="text-center">
            <h1 class="text-4xl font-bold text-slate-900 mb-4">Contact Us</h1>
            <p class="text-xl text-slate-600 max-w-3xl mx-auto">
              We're here to help! Get in touch with our support team for any questions about venue rentals, bookings, or platform features.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
        <!-- Contact Form -->
        <div class="bg-white rounded-xl shadow-card border border-slate-200 p-8">
          <h2 class="text-2xl font-bold text-slate-900 mb-6">Send us a message</h2>
          
          <form id="contact-form" class="space-y-6">
            <!-- Name -->
            <div>
              <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                required
                class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Enter your full name"
              />
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="Enter your email address"
              />
            </div>

            <!-- Subject -->
            <div>
              <label for="subject" class="block text-sm font-medium text-slate-700 mb-2">
                Subject *
              </label>
              <select
                id="subject"
                name="subject"
                required
                class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              >
                <option value="">Select a subject</option>
                <option value="booking-support">Booking Support</option>
                <option value="venue-listing">Venue Listing Help</option>
                <option value="payment-issues">Payment Issues</option>
                <option value="account-help">Account Help</option>
                <option value="technical-issues">Technical Issues</option>
                <option value="partnership">Partnership Inquiry</option>
                <option value="other">Other</option>
              </select>
            </div>

            <!-- Message -->
            <div>
              <label for="message" class="block text-sm font-medium text-slate-700 mb-2">
                Message *
              </label>
              <textarea
                id="message"
                name="message"
                rows="6"
                required
                class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-none"
                placeholder="Tell us how we can help you..."
              ></textarea>
            </div>

            <!-- Submit Button -->
            <button
              type="submit"
              class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              id="submit-button"
            >
              Send Message
            </button>
          </form>

          <!-- Success Message -->
          <div id="success-message" class="hidden mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <p class="text-green-700 font-medium">Message sent successfully!</p>
            </div>
            <p class="text-green-600 text-sm mt-1">We'll get back to you within 24 hours.</p>
          </div>

          <!-- Error Message -->
          <div id="error-message" class="hidden mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
              <p class="text-red-700 font-medium">Failed to send message</p>
            </div>
            <p class="text-red-600 text-sm mt-1">Please try again or contact us directly.</p>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="space-y-8">
          <!-- Contact Details -->
          <div class="bg-white rounded-xl shadow-card border border-slate-200 p-8">
            <h2 class="text-2xl font-bold text-slate-900 mb-6">Get in touch</h2>
            
            <div class="space-y-6">
              <!-- Email -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-slate-900">Email Support</h3>
                  <p class="text-slate-600 mt-1"><EMAIL></p>
                  <p class="text-sm text-slate-500 mt-1">We typically respond within 24 hours</p>
                </div>
              </div>

              <!-- Phone -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-slate-900">Phone Support</h3>
                  <p class="text-slate-600 mt-1">+234 (0) ************</p>
                  <p class="text-sm text-slate-500 mt-1">Monday - Friday, 9:00 AM - 6:00 PM WAT</p>
                </div>
              </div>

              <!-- Live Chat -->
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-slate-900">Live Chat</h3>
                  <p class="text-slate-600 mt-1">Available on our website</p>
                  <p class="text-sm text-slate-500 mt-1">Monday - Friday, 9:00 AM - 6:00 PM WAT</p>
                </div>
              </div>
            </div>
          </div>

          <!-- FAQ Link -->
          <div class="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl border border-primary-200 p-8">
            <h3 class="text-xl font-bold text-slate-900 mb-4">Frequently Asked Questions</h3>
            <p class="text-slate-600 mb-6">
              Find quick answers to common questions about bookings, payments, and venue listings.
            </p>
            <a
              href="/help"
              class="inline-flex items-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
            >
              Visit FAQ
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </div>

          <!-- Business Hours -->
          <div class="bg-white rounded-xl shadow-card border border-slate-200 p-8">
            <h3 class="text-xl font-bold text-slate-900 mb-4">Business Hours</h3>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-slate-600">Monday - Friday</span>
                <span class="text-slate-900 font-medium">9:00 AM - 6:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">Saturday</span>
                <span class="text-slate-900 font-medium">10:00 AM - 4:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">Sunday</span>
                <span class="text-slate-900 font-medium">Closed</span>
              </div>
            </div>
            <p class="text-sm text-slate-500 mt-4">
              All times are in West Africa Time (WAT)
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Links -->
    <div class="bg-slate-50 border-t border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center">
          <h3 class="text-lg font-semibold text-slate-900 mb-6">Quick Links</h3>
          <div class="flex flex-wrap justify-center gap-4">
            <a href="/" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Home
            </a>
            <a href="/venues" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Browse Venues
            </a>
            <a href="/help" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Help Center
            </a>
            <a href="/terms" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Terms of Service
            </a>
            <a href="/privacy" class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
              Privacy Policy
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>
</BaseLayout>

<script>
  // Contact form handling
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('contact-form') as HTMLFormElement;
    const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      // Show loading state
      submitButton.disabled = true;
      submitButton.textContent = 'Sending...';

      // Hide previous messages
      successMessage?.classList.add('hidden');
      errorMessage?.classList.add('hidden');

      try {
        const formData = new FormData(form);
        const data = {
          name: formData.get('name'),
          email: formData.get('email'),
          subject: formData.get('subject'),
          message: formData.get('message'),
          timestamp: new Date().toISOString()
        };

        // Simulate API call (replace with actual endpoint)
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Show success message
        successMessage?.classList.remove('hidden');
        form.reset();

        // Log for development (remove in production)
        console.log('Contact form submission:', data);

      } catch (error) {
        console.error('Failed to send message:', error);
        errorMessage?.classList.remove('hidden');
      } finally {
        // Reset button state
        submitButton.disabled = false;
        submitButton.textContent = 'Send Message';
      }
    });
  });
</script>

<style>
  /* Custom styles for contact page */
  .shadow-card {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  /* Form focus styles */
  input:focus,
  select:focus,
  textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .max-w-7xl {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .grid-cols-1.lg\\:grid-cols-2 {
      gap: 2rem;
    }
  }
</style>
