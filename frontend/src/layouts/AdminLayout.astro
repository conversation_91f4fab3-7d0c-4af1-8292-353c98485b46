---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Admin Dashboard - Trodoo" } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="description" content={description} />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <title>{title}</title>
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
</head>

<body class="bg-[#F9FAFB] font-['Roboto',sans-serif]">
  <!-- Auth Check Loading -->
  <div id="auth-loading" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
    <div class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-[#059669] mx-auto mb-4"></div>
      <p class="text-[#6B7280]">Verifying access...</p>
    </div>
  </div>

  <!-- Unauthorized Access -->
  <div id="unauthorized" class="hidden fixed inset-0 bg-white z-50 flex items-center justify-center">
    <div class="text-center max-w-md mx-auto px-6">
      <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      <h1 class="text-2xl font-bold text-[#1F2937] mb-2">Access Denied</h1>
      <p class="text-[#6B7280] mb-6">
        You don't have permission to access the admin dashboard. Please contact an administrator if you believe this is an error.
      </p>
      <div class="space-y-3">
        <a 
          href="/dashboard" 
          class="block w-full px-4 py-2 bg-[#F59E0B] text-[#1F2937] font-medium rounded-lg hover:bg-[#D97706] transition-colors duration-300"
        >
          Go to Dashboard
        </a>
        <a 
          href="/auth/login" 
          class="block w-full px-4 py-2 border-2 border-[#E5E7EB] text-[#6B7280] font-medium rounded-lg hover:bg-[#F9FAFB] transition-colors duration-300"
        >
          Sign In as Different User
        </a>
      </div>
    </div>
  </div>

  <!-- Admin Layout -->
  <div id="admin-content" class="hidden">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b border-[#E5E7EB]">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <!-- Logo and Title -->
          <div class="flex items-center space-x-4">
            <a href="/admin" class="flex items-center space-x-3">
              <h1 class="text-2xl font-bold text-[#059669]">Trodoo</h1>
              <span class="px-2 py-1 bg-[#059669] text-white text-xs font-medium rounded-full">
                ADMIN
              </span>
            </a>
          </div>

          <!-- Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a 
              href="/admin" 
              class="text-[#6B7280] hover:text-[#059669] font-medium transition-colors duration-300"
              data-nav="dashboard"
            >
              Dashboard
            </a>
            <a 
              href="/admin/users" 
              class="text-[#6B7280] hover:text-[#059669] font-medium transition-colors duration-300"
              data-nav="users"
            >
              User Management
            </a>
            <a 
              href="/admin/flagged-content" 
              class="text-[#6B7280] hover:text-[#059669] font-medium transition-colors duration-300"
              data-nav="flagged"
            >
              Flagged Content
            </a>
          </nav>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <span class="text-sm text-[#6B7280]" id="admin-user-name">Loading...</span>
            <div class="relative">
              <button 
                id="user-menu-button"
                class="flex items-center space-x-2 text-[#6B7280] hover:text-[#059669] transition-colors duration-300"
              >
                <div class="w-8 h-8 bg-[#059669] rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium" id="admin-user-initial">A</span>
                </div>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              
              <!-- Dropdown Menu -->
              <div 
                id="user-menu" 
                class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-[#E5E7EB] py-1 z-10"
              >
                <a 
                  href="/dashboard" 
                  class="block px-4 py-2 text-sm text-[#6B7280] hover:bg-[#F9FAFB] hover:text-[#059669] transition-colors duration-300"
                >
                  User Dashboard
                </a>
                <a 
                  href="/dashboard/profile" 
                  class="block px-4 py-2 text-sm text-[#6B7280] hover:bg-[#F9FAFB] hover:text-[#059669] transition-colors duration-300"
                >
                  Profile Settings
                </a>
                <hr class="my-1 border-[#E5E7EB]">
                <button 
                  id="logout-button"
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-300"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Admin Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <slot />
    </main>
  </div>

  <script>
    import { userStore, isAdminStore, authLoadingStore, authActions, initializeAuth } from '../lib/state.ts';
    
    document.addEventListener('DOMContentLoaded', async () => {
      const authLoading = document.getElementById('auth-loading');
      const unauthorized = document.getElementById('unauthorized');
      const adminContent = document.getElementById('admin-content');
      const userMenuButton = document.getElementById('user-menu-button');
      const userMenu = document.getElementById('user-menu');
      const logoutButton = document.getElementById('logout-button');

      // Initialize authentication
      initializeAuth();

      // Check admin access
      const checkAdminAccess = () => {
        const user = userStore.get();
        const isAdmin = isAdminStore.get();
        const isLoading = authLoadingStore.get();

        if (isLoading) {
          // Still loading, show loading screen
          authLoading?.classList.remove('hidden');
          unauthorized?.classList.add('hidden');
          adminContent?.classList.add('hidden');
          return;
        }

        if (!user || !isAdmin) {
          // Not authenticated or not admin, show unauthorized
          authLoading?.classList.add('hidden');
          unauthorized?.classList.remove('hidden');
          adminContent?.classList.add('hidden');
          
          // Redirect after 3 seconds
          setTimeout(() => {
            window.location.href = user ? '/dashboard' : '/auth/login';
          }, 3000);
          return;
        }

        // User is admin, show admin content
        authLoading?.classList.add('hidden');
        unauthorized?.classList.add('hidden');
        adminContent?.classList.remove('hidden');
        
        // Update user info in header
        updateUserInfo(user);
        updateActiveNavigation();
      };

      // Subscribe to auth state changes
      userStore.subscribe(checkAdminAccess);
      isAdminStore.subscribe(checkAdminAccess);
      authLoadingStore.subscribe(checkAdminAccess);

      // Initial check
      checkAdminAccess();

      // Update user info in header
      function updateUserInfo(user: any) {
        const userNameEl = document.getElementById('admin-user-name');
        const userInitialEl = document.getElementById('admin-user-initial');
        
        if (userNameEl && user.name) {
          userNameEl.textContent = user.name;
        }
        
        if (userInitialEl && user.name) {
          userInitialEl.textContent = user.name.charAt(0).toUpperCase();
        }
      }

      // Update active navigation
      function updateActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('[data-nav]');
        
        navLinks.forEach(link => {
          const href = link.getAttribute('href');
          if (href && (currentPath === href || (href !== '/admin' && currentPath.startsWith(href)))) {
            link.classList.remove('text-[#6B7280]');
            link.classList.add('text-[#059669]', 'font-bold');
          }
        });
      }

      // User menu toggle
      userMenuButton?.addEventListener('click', (e) => {
        e.stopPropagation();
        userMenu?.classList.toggle('hidden');
      });

      // Close menu when clicking outside
      document.addEventListener('click', () => {
        userMenu?.classList.add('hidden');
      });

      // Logout functionality
      logoutButton?.addEventListener('click', async () => {
        try {
          authActions.logout();
        } catch (error) {
          console.error('Logout error:', error);
          // Force redirect even if logout fails
          window.location.href = '/auth/login';
        }
      });
    });
  </script>
</body>
</html>
