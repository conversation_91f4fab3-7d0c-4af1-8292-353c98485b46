// Paystack payment integration for Trodoo
import type {
  PaystackConfig,
  PaystackPaymentData,
  PaystackResponse,
  PaystackVerificationResponse,
  PaymentInitialization,
  PaymentVerification
} from '../types/payment.ts';

// Paystack types for browser environment
interface PaystackWindow {
  PaystackPop?: {
    setup: (config: {
      key: string;
      email: string;
      amount: number;
      currency: string;
      ref: string;
      metadata?: Record<string, unknown>;
      channels?: string[];
      callback: (response: { reference: string }) => void;
      onClose: () => void;
    }) => {
      openIframe: () => void;
    };
  };
}

// Extend globalThis for browser compatibility
declare global {
  var window: PaystackWindow | undefined;
}

// Paystack configuration
const PAYSTACK_PUBLIC_KEY = import.meta.env.PUBLIC_PAYSTACK_PUBLIC_KEY || '';
const PAYSTACK_SECRET_KEY = import.meta.env.PAYSTACK_SECRET_KEY || '';
const PAYSTACK_BASE_URL = 'https://api.paystack.co';

export const paystackConfig: PaystackConfig = {
  publicKey: PAYSTACK_PUBLIC_KEY,
  currency: 'NGN',
  channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
};

// Generate unique payment reference
export function generatePaymentReference(bookingId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `trodoo_${bookingId}_${timestamp}_${random}`;
}

// Initialize payment with Paystack
export async function initializePayment(paymentData: PaymentInitialization): Promise<{
  success: boolean;
  data?: PaystackResponse['data'];
  error?: string;
}> {
  try {
    const paystackData: PaystackPaymentData = {
      email: paymentData.customer_email,
      amount: Math.round(paymentData.amount * 100), // Convert to kobo
      currency: paymentData.currency,
      reference: paymentData.reference,
      callback_url: paymentData.callback_url,
      metadata: {
        booking_id: paymentData.booking_id,
        venue_id: paymentData.metadata.venue_id as string,
        renter_id: paymentData.metadata.renter_id as string,
        owner_id: paymentData.metadata.owner_id as string,
        custom_fields: [
          {
            display_name: 'Booking ID',
            variable_name: 'booking_id',
            value: paymentData.booking_id
          }
        ]
      },
      channels: paystackConfig.channels
    };

    const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(paystackData)
    });

    const result: PaystackResponse = await response.json();

    if (!response.ok || !result.status) {
      return {
        success: false,
        error: result.message || 'Failed to initialize payment'
      };
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Payment initialization error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment initialization failed'
    };
  }
}

// Verify payment with Paystack
export async function verifyPayment(reference: string): Promise<{
  success: boolean;
  data?: PaymentVerification;
  error?: string;
}> {
  try {
    const response = await fetch(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const result: PaystackVerificationResponse = await response.json();

    if (!response.ok || !result.status) {
      return {
        success: false,
        error: result.message || 'Failed to verify payment'
      };
    }

    const verificationData: PaymentVerification = {
      reference: result.data.reference,
      status: result.data.status,
      amount: result.data.amount / 100, // Convert from kobo
      currency: result.data.currency,
      paid_at: result.data.paid_at,
      gateway_response: result.data.gateway_response,
      customer_details: {
        email: result.data.customer.email,
        phone: result.data.customer.phone,
        name: `${result.data.customer.first_name} ${result.data.customer.last_name}`.trim()
      }
    };

    return {
      success: true,
      data: verificationData
    };
  } catch (error) {
    console.error('Payment verification error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment verification failed'
    };
  }
}

// Calculate platform fees
export function calculateFees(amount: number, platformFeeRate = 0.05) {
  const platformFee = Math.round(amount * platformFeeRate);
  const paymentProcessingFee = Math.round(amount * 0.015); // 1.5% Paystack fee
  const totalAmount = amount + paymentProcessingFee;
  const payoutAmount = amount - platformFee;

  return {
    subtotal: amount,
    platform_fee_rate: platformFeeRate,
    platform_fee: platformFee,
    payment_processing_fee: paymentProcessingFee,
    total_amount: totalAmount,
    payout_amount: payoutAmount,
    currency: 'NGN'
  };
}

// Client-side payment popup (for browser environments)
export function openPaystackPopup(
  paymentData: PaystackPaymentData,
  onSuccess: (response: { reference: string }) => void,
  onClose: () => void
): void {
  // This function would be used in the browser with Paystack's inline JS
  const browserWindow = typeof globalThis !== 'undefined' && 'window' in globalThis ? globalThis.window as PaystackWindow : undefined;
  if (browserWindow?.PaystackPop) {
    const handler = browserWindow.PaystackPop.setup({
      key: paystackConfig.publicKey,
      email: paymentData.email,
      amount: paymentData.amount,
      currency: paymentData.currency,
      ref: paymentData.reference,
      metadata: paymentData.metadata,
      channels: paymentData.channels,
      callback: onSuccess,
      onClose: onClose
    });
    
    handler.openIframe();
  } else {
    console.error('Paystack popup not available');
    onClose();
  }
}

// Validate webhook signature
export async function validateWebhookSignature(
  body: string,
  signature: string,
  secret: string
): Promise<boolean> {
  try {
    const crypto = await import('crypto');
    const hash = crypto.createHmac('sha512', secret).update(body).digest('hex');
    return hash === signature;
  } catch (error) {
    console.error('Webhook signature validation error:', error);
    return false;
  }
}

// Format amount for display
export function formatAmount(amount: number, currency = 'NGN'): string {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount);
}

// Validate payment data
export function validatePaymentData(data: Partial<PaymentInitialization>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!data.booking_id) {
    errors.push('Booking ID is required');
  }

  if (!data.amount || data.amount <= 0) {
    errors.push('Valid amount is required');
  }

  if (!data.customer_email || !/\S+@\S+\.\S+/.test(data.customer_email)) {
    errors.push('Valid email is required');
  }

  if (!data.currency) {
    errors.push('Currency is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export default {
  config: paystackConfig,
  generateReference: generatePaymentReference,
  initialize: initializePayment,
  verify: verifyPayment,
  calculateFees,
  openPopup: openPaystackPopup,
  validateSignature: validateWebhookSignature,
  formatAmount,
  validateData: validatePaymentData
};
