import { MeiliSearch } from 'meilisearch';

// Get MeiliSearch configuration from environment variables
const MEILISEARCH_HOST = import.meta.env.MEILISEARCH_HOST || 'http://localhost:7700';
const MEILISEARCH_API_KEY = import.meta.env.MEILISEARCH_API_KEY || '';

// Create MeiliSearch client instance
let client = null;

export function getMeiliSearchClient() {
  if (!client) {
    client = new MeiliSearch({
      host: MEILISEARCH_HOST,
      apiKey: MEILISEARCH_API_KEY,
    });
  }
  return client;
}

// Export the singleton instance
export const meiliClient = getMeiliSearchClient();

// Index names
export const INDEXES = {
  VENUES: 'venues',
  USERS: 'users', // For admin search functionality
};

// Search configuration
const SEARCH_CONFIG = {
  venues: {
    searchableAttributes: [
      'title',
      'description',
      'address.street',
      'address.city',
      'address.country',
      'amenities',
      'owner.name'
    ],
    filterableAttributes: [
      'capacity',
      'price_per_hour',
      'amenities',
      'is_published',
      'owner.id',
      'address.city',
      'address.country',
      'average_rating'
    ],
    sortableAttributes: [
      'price_per_hour',
      'capacity',
      'created',
      'updated',
      'average_rating'
    ],
    displayedAttributes: [
      'id',
      'title',
      'description',
      'address',
      'capacity',
      'price_per_hour',
      'amenities',
      'standard_photos',
      'pano_photo',
      'average_rating',
      'review_count',
      'owner',
      'created',
      'updated',
      'is_published'
    ],
    rankingRules: [
      'words',
      'typo',
      'proximity',
      'attribute',
      'sort',
      'exactness'
    ],
    stopWords: ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'],
    synonyms: {
      'wifi': ['wi-fi', 'internet', 'wireless'],
      'ac': ['air conditioning', 'aircon'],
      'parking': ['car park', 'garage']
    }
  }
};

// Helper functions for venue search
export async function searchVenues(query, options = {}) {
  try {
    const {
      limit = 20,
      offset = 0,
      filters = '',
      sort = [],
      facets = []
    } = options;

    const searchParams = {
      limit,
      offset,
      filter: filters,
      sort,
      facets
    };

    const index = meiliClient.index(INDEXES.VENUES);
    const results = await index.search(query, searchParams);
    
    return {
      success: true,
      hits: results.hits,
      totalHits: results.estimatedTotalHits,
      facetDistribution: results.facetDistribution,
      processingTimeMs: results.processingTimeMs
    };
  } catch (error) {
    console.error('Search failed:', error);
    return {
      success: false,
      error: error.message,
      hits: [],
      totalHits: 0
    };
  }
}

// Advanced search with filters
export async function searchVenuesWithFilters(query, filters = {}) {
  const filterStrings = [];
  
  // Price range filter
  if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
    if (filters.minPrice !== undefined && filters.maxPrice !== undefined) {
      filterStrings.push(`price_per_hour ${filters.minPrice} TO ${filters.maxPrice}`);
    } else if (filters.minPrice !== undefined) {
      filterStrings.push(`price_per_hour >= ${filters.minPrice}`);
    } else if (filters.maxPrice !== undefined) {
      filterStrings.push(`price_per_hour <= ${filters.maxPrice}`);
    }
  }
  
  // Capacity filter
  if (filters.minCapacity !== undefined) {
    filterStrings.push(`capacity >= ${filters.minCapacity}`);
  }
  
  // Amenities filter
  if (filters.amenities && filters.amenities.length > 0) {
    const amenityFilters = filters.amenities.map(amenity => `amenities = "${amenity}"`);
    filterStrings.push(`(${amenityFilters.join(' OR ')})`);
  }
  
  // Location filter (if implementing geo search)
  if (filters.location) {
    // This would require geo search setup in MeiliSearch
    // filterStrings.push(`_geoRadius(${filters.location.lat}, ${filters.location.lng}, ${filters.location.radius})`);
  }
  
  // Only published venues
  filterStrings.push('is_published = true');
  
  const filterString = filterStrings.join(' AND ');
  
  return searchVenues(query, {
    filters: filterString,
    sort: filters.sort || [],
    limit: filters.limit || 20,
    offset: filters.offset || 0,
    facets: ['amenities', 'capacity', 'price_per_hour']
  });
}

// Get search suggestions/autocomplete
export async function getSearchSuggestions(query, limit = 5) {
  try {
    if (!query || query.length < 2) {
      return { success: true, suggestions: [] };
    }
    
    const results = await searchVenues(query, { limit });
    
    const suggestions = results.hits.map(hit => ({
      id: hit.id,
      title: hit.title,
      address: hit.address,
      type: 'venue'
    }));
    
    return { success: true, suggestions };
  } catch (error) {
    console.error('Failed to get suggestions:', error);
    return { success: false, suggestions: [] };
  }
}

// Sync venue data to MeiliSearch
export async function syncVenueToSearch(venue) {
  try {
    const index = meiliClient.index(INDEXES.VENUES);

    // Transform venue data for search
    const searchDocument = {
      id: venue.id,
      title: venue.title,
      description: venue.description,
      address: venue.address,
      capacity: venue.capacity,
      price_per_hour: venue.price_per_hour,
      amenities: venue.amenities || [],
      standard_photos: venue.standard_photos || [],
      pano_photo: venue.pano_photo,
      is_published: venue.is_published,
      owner: venue.expand?.owner ? {
        id: venue.expand.owner.id,
        name: venue.expand.owner.name,
        email: venue.expand.owner.email
      } : { id: venue.owner },
      average_rating: venue.average_rating || 0,
      review_count: venue.review_count || 0,
      created: venue.created,
      updated: venue.updated
    };

    await index.addDocuments([searchDocument]);
    return { success: true };
  } catch (error) {
    console.error('Failed to sync venue to search:', error);
    return { success: false, error: error.message };
  }
}

// Remove venue from search index
export async function removeVenueFromSearch(venueId) {
  try {
    const index = meiliClient.index(INDEXES.VENUES);
    await index.deleteDocument(venueId);
    return { success: true };
  } catch (error) {
    console.error('Failed to remove venue from search:', error);
    return { success: false, error: error.message };
  }
}

// Bulk sync venues to search
export async function bulkSyncVenuesToSearch(venues) {
  try {
    const index = meiliClient.index(INDEXES.VENUES);

    const searchDocuments = venues.map(venue => ({
      id: venue.id,
      title: venue.title,
      description: venue.description,
      address: venue.address,
      capacity: venue.capacity,
      price_per_hour: venue.price_per_hour,
      amenities: venue.amenities || [],
      standard_photos: venue.standard_photos || [],
      pano_photo: venue.pano_photo,
      is_published: venue.is_published,
      owner: venue.expand?.owner ? {
        id: venue.expand.owner.id,
        name: venue.expand.owner.name,
        email: venue.expand.owner.email
      } : { id: venue.owner },
      average_rating: venue.average_rating || 0,
      review_count: venue.review_count || 0,
      created: venue.created,
      updated: venue.updated
    }));

    await index.addDocuments(searchDocuments);
    return { success: true, count: searchDocuments.length };
  } catch (error) {
    console.error('Failed to bulk sync venues to search:', error);
    return { success: false, error: error.message };
  }
}

// Initialize search indexes (called during app setup)
export async function initializeSearchIndexes() {
  try {
    const venuesIndex = meiliClient.index(INDEXES.VENUES);

    // Update search settings
    await venuesIndex.updateSettings(SEARCH_CONFIG.venues);

    console.log('Search indexes initialized successfully');
    return { success: true };
  } catch (error) {
    console.error('Failed to initialize search indexes:', error);
    return { success: false, error: error.message };
  }
}

// Health check for MeiliSearch
export async function checkSearchHealth() {
  try {
    const health = await meiliClient.health();
    return { success: true, status: health.status };
  } catch (error) {
    console.error('Search health check failed:', error);
    return { success: false, error: error.message };
  }
}

export default meiliClient;
