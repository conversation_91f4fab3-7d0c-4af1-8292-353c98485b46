/**
 * File upload utilities for venue media management
 */

export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileUploadResult {
  success: boolean;
  url?: string;
  filename?: string;
  error?: string;
}

export interface FileValidationOptions {
  maxSize?: number; // in bytes
  allowedTypes?: readonly string[];
  maxFiles?: number;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

const DEFAULT_IMAGE_OPTIONS: Required<FileValidationOptions> = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  maxFiles: 10
};

const DEFAULT_PDF_OPTIONS: Required<FileValidationOptions> = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['application/pdf'],
  maxFiles: 1
};

/**
 * Validates a single file against the given options
 */
export function validateFile(file: File, options: FileValidationOptions = {}): FileValidationResult {
  const opts = { ...DEFAULT_IMAGE_OPTIONS, ...options };
  const errors: string[] = [];
  const warnings: string[] = [];

  // File size validation
  if (file.size > opts.maxSize) {
    errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(opts.maxSize)})`);
  }

  // File type validation
  if (!opts.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${opts.allowedTypes.join(', ')}`);
  }

  // Performance warnings
  if (file.size > opts.maxSize * 0.8) {
    warnings.push('Large file size may affect upload performance');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates multiple files
 */
export function validateFiles(files: File[], options: FileValidationOptions = {}): FileValidationResult {
  const opts = { ...DEFAULT_IMAGE_OPTIONS, ...options };
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check file count
  if (files.length > opts.maxFiles) {
    errors.push(`Too many files selected. Maximum allowed: ${opts.maxFiles}`);
  }

  // Validate each file
  files.forEach((file, index) => {
    const result = validateFile(file, options);
    if (!result.isValid) {
      errors.push(`File ${index + 1} (${file.name}): ${result.errors.join(', ')}`);
    }
    warnings.push(...result.warnings.map(w => `File ${index + 1} (${file.name}): ${w}`));
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Uploads a file to PocketBase with progress tracking
 */
export async function uploadFile(
  file: File,
  collection: string,
  recordId: string,
  fieldName: string,
  onProgress?: (progress: FileUploadProgress) => void
): Promise<FileUploadResult> {
  try {
    const formData = new FormData();
    formData.append(fieldName, file);

    const xhr = new XMLHttpRequest();

    return new Promise<FileUploadResult>((resolve) => {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress: FileUploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          };
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve({
              success: true,
              url: response[fieldName],
              filename: file.name
            });
          } catch (error) {
            resolve({
              success: false,
              error: 'Invalid response from server'
            });
          }
        } else {
          resolve({
            success: false,
            error: `Upload failed with status ${xhr.status}`
          });
        }
      });

      xhr.addEventListener('error', () => {
        resolve({
          success: false,
          error: 'Network error during upload'
        });
      });

      xhr.addEventListener('timeout', () => {
        resolve({
          success: false,
          error: 'Upload timeout'
        });
      });

      // Set timeout to 5 minutes
      xhr.timeout = 5 * 60 * 1000;

      // Make the request
      xhr.open('PATCH', `/api/collections/${collection}/records/${recordId}`);
      xhr.send(formData);
    });

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
}

/**
 * Uploads multiple files sequentially
 */
export async function uploadFiles(
  files: File[],
  collection: string,
  recordId: string,
  fieldName: string,
  onProgress?: (fileIndex: number, progress: FileUploadProgress) => void,
  onFileComplete?: (fileIndex: number, result: FileUploadResult) => void
): Promise<FileUploadResult[]> {
  const results: FileUploadResult[] = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    const result = await uploadFile(
      file,
      collection,
      recordId,
      fieldName,
      (progress) => onProgress?.(i, progress)
    );

    results.push(result);
    onFileComplete?.(i, result);

    // Stop if upload failed and it's a critical error
    if (!result.success && result.error?.includes('Network error')) {
      break;
    }
  }

  return results;
}

/**
 * Creates a preview URL for a file
 */
export function createFilePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Revokes a preview URL to free memory
 */
export function revokeFilePreview(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Compresses an image file
 */
export async function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Formats file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Gets file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * Checks if a file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Checks if a file is a PDF
 */
export function isPDFFile(file: File): boolean {
  return file.type === 'application/pdf';
}

/**
 * File upload validation presets
 */
export const FILE_VALIDATION_PRESETS = {
  VENUE_PHOTOS: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    maxFiles: 10
  },
  PANORAMA_PHOTO: {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
    maxFiles: 1
  },
  RENTAL_AGREEMENT: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['application/pdf'],
    maxFiles: 1
  },
  PROFILE_AVATAR: {
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
    maxFiles: 1
  }
} as const;
