/*
	Installed from https://reactbits.dev/ts/tailwind/
*/

import {
  motion,
  MotionValue,
  useMotionValue,
  useSpring,
  useTransform,
  type SpringOptions,
  AnimatePresence,
} from "framer-motion";
import React, {
  Children,
  cloneElement,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

export type DockItemData = {
  icon: React.ReactNode;
  label: React.ReactNode;
  onClick: () => void;
  className?: string;
  highlight?: boolean;
};

export type DockProps = {
  items: DockItemData[];
  className?: string;
  distance?: number;
  panelHeight?: number;
  baseItemSize?: number;
  dockHeight?: number;
  magnification?: number;
  spring?: SpringOptions;
};

type DockItemProps = {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  mouseX: MotionValue;
  spring: SpringOptions;
  distance: number;
  baseItemSize: number;
  magnification: number;
  highlight?: boolean;
};

function DockItem({
  children,
  className = "",
  onClick,
  mouseX,
  spring,
  distance,
  magnification,
  baseItemSize,
  highlight,
}: DockItemProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isHovered = useMotionValue(0);

  const mouseDistance = useTransform(mouseX, (val) => {
    const rect = ref.current?.getBoundingClientRect() ?? {
      x: 0,
      width: baseItemSize,
    };
    return val - rect.x - baseItemSize / 2;
  });

  const targetSize = useTransform(
    mouseDistance,
    [-distance, 0, distance],
    [baseItemSize, magnification, baseItemSize],
  );
  const size = useSpring(targetSize, spring);

  return (
    <motion.div
      ref={ref}
      style={{
        width: size,
        height: size,
        borderColor: highlight ? '#F59E0B' : '#E5E7EB', // secondary.DEFAULT or neutral.mediumGray
        backgroundColor: highlight ? '#FEF3C7' : '#FFFFFF', // secondary.subtle or neutral.white
        borderRadius: '8px', // md border radius
        boxShadow: highlight ? '0 8px 16px rgba(0, 0, 0, 0.1)' : '0 4px 8px rgba(0, 0, 0, 0.05)', // medium or subtle shadow
      }}
      onHoverStart={() => isHovered.set(1)}
      onHoverEnd={() => isHovered.set(0)}
      onFocus={() => isHovered.set(1)}
      onBlur={() => isHovered.set(0)}
      onClick={onClick}
      className={`relative inline-flex items-center justify-center border-2 transition-all duration-300 ease-in-out ${className}`}
      onMouseEnter={(e) => {
        e.currentTarget.style.borderColor = '#059669'; // refined primary.green - more subtle
        e.currentTarget.style.backgroundColor = '#D1FAE5'; // primary.subtle - very light green instead of yellow
        e.currentTarget.style.transform = 'translateY(-1px)'; // less dramatic lift
        e.currentTarget.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.1)'; // medium shadow
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.borderColor = '#E5E7EB'; // refined neutral.mediumGray
        e.currentTarget.style.backgroundColor = '#FFFFFF'; // neutral.white
        e.currentTarget.style.transform = 'translateY(0px)';
        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)'; // subtle shadow
      }}
      tabIndex={0}
      role="button"
      aria-haspopup="true"
    >
      {Children.map(children, (child) =>
        cloneElement(child as React.ReactElement, { isHovered }),
      )}
    </motion.div>
  );
}

type DockLabelProps = {
  className?: string;
  children: React.ReactNode;
};

function DockLabel({ children, className = "", ...rest }: DockLabelProps) {
  const { isHovered } = rest as { isHovered: MotionValue<number> };
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const unsubscribe = isHovered.on("change", (latest) => {
      setIsVisible(latest === 1);
    });
    return () => unsubscribe();
  }, [isHovered]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: 1, y: -10 }}
          exit={{ opacity: 0, y: 0 }}
          transition={{ duration: 0.2 }}
          className={`${className} absolute -top-8 left-1/2 w-fit whitespace-pre text-white`}
          style={{
            borderRadius: '8px', // md border radius
            border: '1px solid #E5E7EB', // refined neutral.mediumGray
            backgroundColor: '#1F2937', // refined neutral.black - softer
            padding: '8px 16px', // md spacing
            fontSize: '14px', // small body text
            fontWeight: 500, // medium weight
            fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif",
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)', // medium shadow
            x: "-50%"
          }}
          role="tooltip"
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

type DockIconProps = {
  className?: string;
  children: React.ReactNode;
};

function DockIcon({ children, className = "" }: DockIconProps) {
  return (
    <div
      className={`flex items-center justify-center transition-colors duration-300 ease-in-out ${className}`}
      style={{
        color: '#6B7280', // neutral.darkGray - more subtle default state
      }}
    >
      {children}
    </div>
  );
}

export default function Dock({
  items,
  className = "",
  spring = { mass: 0.1, stiffness: 150, damping: 12 },
  magnification = 70,
  distance = 200,
  panelHeight = 64,
  dockHeight = 256,
  baseItemSize = 50,
}: DockProps) {
  const mouseX = useMotionValue(Infinity);
  const isHovered = useMotionValue(0);

  const maxHeight = useMemo(
    () => Math.max(dockHeight, magnification + magnification / 2 + 4),
    [magnification],
  );
  const heightRow = useTransform(isHovered, [0, 1], [panelHeight, maxHeight]);
  const height = useSpring(heightRow, spring);

  return (
    <motion.div
      style={{ height, scrollbarWidth: "none" }}
      className="mx-2 flex max-w-full items-center"
    >
      <motion.div
        onMouseMove={({ pageX }) => {
          isHovered.set(1);
          mouseX.set(pageX);
        }}
        onMouseLeave={() => {
          isHovered.set(0);
          mouseX.set(Infinity);
        }}
        className={`${className} absolute bottom-2 left-1/2 transform -translate-x-1/2 flex items-end w-fit border-2 backdrop-blur-md`}
        style={{
          gap: '24px', // lg spacing - slightly tighter
          borderRadius: '20px', // xl border radius
          borderColor: '#E5E7EB', // refined neutral.mediumGray
          backgroundColor: 'rgba(255, 255, 255, 0.95)', // slightly more opaque for better contrast
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.05)', // subtle shadow - less prominent
          paddingBottom: '16px', // md spacing
          paddingLeft: '24px', // lg spacing - more compact
          paddingRight: '24px', // lg spacing
          height: panelHeight
        }}
        role="toolbar"
        aria-label="Application dock"
      >
        {items.map((item, index) => (
          <DockItem
            key={index}
            onClick={item.onClick}
            className={item.className}
            highlight={item.highlight}
            mouseX={mouseX}
            spring={spring}
            distance={distance}
            magnification={magnification}
            baseItemSize={baseItemSize}
          >
            <DockIcon>{item.icon}</DockIcon>
            <DockLabel>{item.label}</DockLabel>
          </DockItem>
        ))}
      </motion.div>
    </motion.div>
  );
}
