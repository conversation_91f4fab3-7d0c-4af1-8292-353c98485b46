import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Send, User, Clock } from 'lucide-react';
import type { Message } from '../../types/message.ts';
import type { Booking } from '../../types/booking.ts';
import Button from '../common/Button.tsx';
import { 
  createMessage, 
  getBookingMessages, 
  subscribeToBookingMessages 
} from '../../lib/pocketbase.ts';

interface MessagingWindowProps {
  booking: Booking;
  currentUserId: string;
  className?: string;
}

interface MessageFormData {
  content: string;
}

export default function MessagingWindow({ 
  booking, 
  currentUserId, 
  className = '' 
}: MessagingWindowProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [formData, setFormData] = useState<MessageFormData>({ content: '' });
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Load initial messages
  const loadMessages = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getBookingMessages(booking.id);
      if (result.success) {
        setMessages(result.messages);
        setTimeout(scrollToBottom, 100);
      } else {
        setError(result.error || 'Failed to load messages');
      }
    } catch (err) {
      setError('Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  }, [booking.id, scrollToBottom]);

  // Subscribe to real-time message updates
  const subscribeToMessages = useCallback(async () => {
    try {
      const result = await subscribeToBookingMessages(booking.id, (data) => {
        if (data.action === 'create') {
          setMessages(prev => [...prev, data.record as Message]);
          setTimeout(scrollToBottom, 100);
        } else if (data.action === 'update') {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === data.record.id ? { ...msg, ...data.record } : msg
            )
          );
        } else if (data.action === 'delete') {
          setMessages(prev => prev.filter(msg => msg.id !== data.record.id));
        }
      });

      if (result.success && result.unsubscribe) {
        unsubscribeRef.current = result.unsubscribe;
      }
    } catch (err) {
      console.error('Failed to subscribe to messages:', err);
    }
  }, [booking.id, scrollToBottom]);

  // Initialize component
  useEffect(() => {
    loadMessages();
    subscribeToMessages();

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [loadMessages, subscribeToMessages]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.content.trim()) {
      return;
    }

    setIsSending(true);
    setError(null);

    try {
      const result = await createMessage({
        booking: booking.id,
        content: formData.content.trim()
      });

      if (result.success) {
        setFormData({ content: '' });
      } else {
        setError(result.error || 'Failed to send message');
      }
    } catch (err) {
      setError('Failed to send message');
    } finally {
      setIsSending(false);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  // Check if message is from current user
  const isOwnMessage = (message: Message) => {
    return message.sender.id === currentUserId;
  };

  return (
    <div className={`bg-white rounded-xl shadow-card border border-slate-200 ${className}`}>
      {/* Header */}
      <div className="border-b border-slate-200 p-4">
        <h3 className="text-lg font-semibold text-slate-900">
          Messages
        </h3>
        <p className="text-sm text-slate-600 mt-1">
          Chat with {currentUserId === booking.renter.id ? 'the venue owner' : 'the renter'} about this booking
        </p>
      </div>

      {/* Messages Container */}
      <div className="h-96 overflow-y-auto p-4 space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-green"></div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-red-600 text-sm">{error}</p>
              <Button
                onClick={loadMessages}
                variant="outline"
                size="sm"
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <User className="w-12 h-12 text-slate-300 mx-auto mb-3" />
              <p className="text-slate-500 text-sm">No messages yet</p>
              <p className="text-slate-400 text-xs mt-1">Start the conversation!</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${isOwnMessage(message) ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isOwnMessage(message)
                      ? 'bg-primary-green text-white'
                      : 'bg-slate-100 text-slate-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <div className={`flex items-center mt-1 text-xs ${
                    isOwnMessage(message) ? 'text-green-100' : 'text-slate-500'
                  }`}>
                    <Clock className="w-3 h-3 mr-1" />
                    {formatTimestamp(message.created)}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="border-t border-slate-200 p-4">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            type="text"
            value={formData.content}
            onChange={(e) => setFormData({ content: e.target.value })}
            placeholder="Type your message..."
            className="flex-1 px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green"
            disabled={isSending}
          />
          <Button
            type="submit"
            size="sm"
            isLoading={isSending}
            disabled={!formData.content.trim() || isSending}
            className="px-3"
          >
            <Send className="w-4 h-4" />
          </Button>
        </form>
        {error && (
          <p className="text-red-600 text-xs mt-2">{error}</p>
        )}
      </div>
    </div>
  );
}
