import { useStore } from '@nanostores/react';
import { userStore } from '../../lib/state.ts';
import ProfileDropdown from './ProfileDropdown.tsx';

interface AuthenticatedHeaderProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  backButtonText?: string;
  backButtonHref?: string;
  className?: string;
}

export default function AuthenticatedHeader({
  title,
  subtitle,
  showBackButton = false,
  backButtonText = "Back to Dashboard",
  backButtonHref = "/dashboard",
  className = ""
}: AuthenticatedHeaderProps) {
  const user = useStore(userStore);

  return (
    <div className={`bg-white shadow-sm border-b border-[#E5E7EB] ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-[#1F2937]">{title}</h1>
              {subtitle && (
                <p className="mt-1 text-sm text-[#6B7280]">{subtitle}</p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {showBackButton && (
                <a 
                  href={backButtonHref}
                  className="inline-flex items-center px-4 py-2 border-2 border-[#E5E7EB] text-sm font-medium rounded-lg text-[#6B7280] bg-white hover:bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#059669] transition-all duration-300"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                  </svg>
                  {backButtonText}
                </a>
              )}
              
              {/* Profile Dropdown - only show if user is authenticated */}
              {user && <ProfileDropdown />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
