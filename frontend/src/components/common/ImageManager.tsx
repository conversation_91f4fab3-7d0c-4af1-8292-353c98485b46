import React, { useState, useCallback, useRef, useEffect } from 'react';
import { X, Upload, Move, Eye, RotateCw, Download, AlertCircle } from 'lucide-react';
import { createFilePreview, revokeFilePreview, formatFileSize, validateFiles, FILE_VALIDATION_PRESETS } from '../../lib/fileUpload.ts';

interface ImageManagerProps {
  images: File[];
  onImagesChange: (images: File[]) => void;
  maxImages?: number;
  allowReorder?: boolean;
  showPreview?: boolean;
  validationPreset?: keyof typeof FILE_VALIDATION_PRESETS;
  className?: string;
}

interface ImagePreview {
  file: File;
  url: string;
  id: string;
}

export default function ImageManager({
  images,
  onImagesChange,
  maxImages = 10,
  allowReorder = true,
  showPreview = true,
  validationPreset = 'VENUE_PHOTOS',
  className = ''
}: ImageManagerProps) {
  const [previews, setPreviews] = useState<ImagePreview[]>([]);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [previewIndex, setPreviewIndex] = useState<number | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Create previews when images change
  useEffect(() => {
    // Revoke old URLs
    previews.forEach(preview => revokeFilePreview(preview.url));

    // Create new previews
    const newPreviews = images.map((file, index) => ({
      file,
      url: createFilePreview(file),
      id: `${file.name}-${file.lastModified}-${index}`
    }));

    setPreviews(newPreviews);

    // Cleanup on unmount
    return () => {
      newPreviews.forEach(preview => revokeFilePreview(preview.url));
    };
  }, [images]);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files);
    const validationOptions = FILE_VALIDATION_PRESETS[validationPreset];
    
    // Validate files
    const validation = validateFiles(newFiles, {
      ...validationOptions,
      maxFiles: maxImages - images.length
    });

    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setErrors([]);
    const updatedImages = [...images, ...newFiles].slice(0, maxImages);
    onImagesChange(updatedImages);
  }, [images, onImagesChange, maxImages, validationPreset]);

  const handleRemoveImage = useCallback((index: number) => {
    const updatedImages = images.filter((_, i) => i !== index);
    onImagesChange(updatedImages);
  }, [images, onImagesChange]);

  const handleReorderImages = useCallback((fromIndex: number, toIndex: number) => {
    if (!allowReorder) return;

    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    onImagesChange(updatedImages);
  }, [images, onImagesChange, allowReorder]);

  const handleDragStart = useCallback((e: React.DragEvent, index: number) => {
    if (!allowReorder) return;
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  }, [allowReorder]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    if (!allowReorder) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, [allowReorder]);

  const handleDrop = useCallback((e: React.DragEvent, dropIndex: number) => {
    if (!allowReorder || draggedIndex === null) return;
    e.preventDefault();
    
    handleReorderImages(draggedIndex, dropIndex);
    setDraggedIndex(null);
  }, [allowReorder, draggedIndex, handleReorderImages]);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const downloadImage = useCallback((file: File) => {
    const url = createFilePreview(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    revokeFilePreview(url);
  }, []);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-primary-green transition-colors">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        
        <Upload className="w-12 h-12 text-slate-400 mx-auto mb-3" />
        <p className="text-sm text-slate-600 mb-1">
          Click to upload images or drag and drop
        </p>
        <p className="text-xs text-slate-500">
          {images.length}/{maxImages} images • Max {formatFileSize(FILE_VALIDATION_PRESETS[validationPreset].maxSize)} each
        </p>
        
        <button
          type="button"
          onClick={openFileDialog}
          className="mt-3 px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-greenDark transition-colors"
          disabled={images.length >= maxImages}
        >
          Choose Images
        </button>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-600 mb-1">Upload errors:</p>
              <ul className="text-sm text-red-600 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Image Grid */}
      {previews.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {previews.map((preview, index) => (
            <div
              key={preview.id}
              className={`
                relative group bg-slate-100 rounded-lg overflow-hidden aspect-square
                ${allowReorder ? 'cursor-move' : ''}
                ${draggedIndex === index ? 'opacity-50' : ''}
              `}
              draggable={allowReorder}
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
            >
              {/* Image */}
              <img
                src={preview.url}
                alt={`Preview ${index + 1}`}
                className="w-full h-full object-cover"
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                  {/* Preview Button */}
                  {showPreview && (
                    <button
                      type="button"
                      onClick={() => setPreviewIndex(index)}
                      className="p-2 bg-white bg-opacity-90 text-slate-700 rounded-full hover:bg-opacity-100 transition-all"
                      title="Preview"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  )}

                  {/* Download Button */}
                  <button
                    type="button"
                    onClick={() => downloadImage(preview.file)}
                    className="p-2 bg-white bg-opacity-90 text-slate-700 rounded-full hover:bg-opacity-100 transition-all"
                    title="Download"
                  >
                    <Download className="w-4 h-4" />
                  </button>

                  {/* Remove Button */}
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(index)}
                    className="p-2 bg-red-500 bg-opacity-90 text-white rounded-full hover:bg-opacity-100 transition-all"
                    title="Remove"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Drag Handle */}
              {allowReorder && (
                <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Move className="w-4 h-4 text-white drop-shadow-lg" />
                </div>
              )}

              {/* Image Info */}
              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2 text-xs">
                <p className="truncate">{preview.file.name}</p>
                <p className="text-slate-300">{formatFileSize(preview.file.size)}</p>
              </div>

              {/* Primary Badge */}
              {index === 0 && (
                <div className="absolute top-2 right-2 bg-primary-green text-white px-2 py-1 rounded text-xs font-medium">
                  Primary
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Image Preview Modal */}
      {previewIndex !== null && previews[previewIndex] && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={previews[previewIndex].url}
              alt={`Preview ${previewIndex + 1}`}
              className="max-w-full max-h-full object-contain"
            />
            
            {/* Close Button */}
            <button
              onClick={() => setPreviewIndex(null)}
              className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all"
            >
              <X className="w-6 h-6" />
            </button>

            {/* Navigation */}
            {previews.length > 1 && (
              <>
                <button
                  onClick={() => setPreviewIndex(Math.max(0, previewIndex - 1))}
                  disabled={previewIndex === 0}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all disabled:opacity-50"
                >
                  <RotateCw className="w-6 h-6 transform rotate-180" />
                </button>
                
                <button
                  onClick={() => setPreviewIndex(Math.min(previews.length - 1, previewIndex + 1))}
                  disabled={previewIndex === previews.length - 1}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all disabled:opacity-50"
                >
                  <RotateCw className="w-6 h-6" />
                </button>
              </>
            )}

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
              {previewIndex + 1} of {previews.length}
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && (
        <div className="text-center py-8 text-slate-500">
          <Upload className="w-16 h-16 mx-auto mb-4 text-slate-300" />
          <p className="text-lg font-medium mb-2">No images uploaded</p>
          <p className="text-sm">Add some images to get started</p>
        </div>
      )}
    </div>
  );
}
