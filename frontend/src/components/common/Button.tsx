import React from 'react';
import type { ButtonHTMLAttributes, ReactNode } from 'react';
import { cn } from '../../lib/utils.ts';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  children: ReactNode;
}

const buttonVariants = {
  primary: 'bg-secondary-500 text-neutral-black font-bold hover:bg-secondary-600 hover:scale-[1.02] focus:ring-2 focus:ring-secondary-500 focus:ring-opacity-20 shadow-medium hover:shadow-large',
  secondary: 'bg-transparent text-white border-2 border-white font-semibold hover:bg-white hover:text-primary-600 hover:scale-[1.02] focus:ring-2 focus:ring-white focus:ring-opacity-20',
  outline: 'border border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20',
  ghost: 'text-slate-700 hover:bg-slate-100 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-opacity-20',
  glassmorphism: 'bg-white/20 backdrop-blur-xl text-white border border-white/30 hover:bg-white/30 focus:ring-2 focus:ring-white focus:ring-opacity-20',
};

const buttonSizes = {
  sm: 'px-4 py-2 text-sm rounded-full',
  md: 'px-6 py-3 text-base rounded-full',
  lg: 'px-8 py-4 text-lg rounded-full',
};

export default function Button({
  variant = 'primary',
  size = 'md',
  isLoading = false,
  disabled,
  className,
  children,
  ...props
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variantClasses = buttonVariants[variant];
  const sizeClasses = buttonSizes[size];

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses,
        sizeClasses,
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
}
