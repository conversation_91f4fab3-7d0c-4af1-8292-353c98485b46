import { useState } from 'react';
import { useStore } from '@nanostores/react';
import { userStore } from '../../lib/state.ts';
import type { User as _User } from '../../types/user.ts';

interface FlagContentModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentType: 'venue' | 'user_profile';
  contentId: string;
  contentTitle?: string;
  onSuccess?: () => void;
}

const FLAG_REASONS = [
  'Inappropriate content',
  'Misleading information',
  'Spam or fake listing',
  'Offensive language',
  'Copyright violation',
  'Safety concerns',
  'Pricing issues',
  'Other'
];

export default function FlagContentModal({
  isOpen,
  onClose,
  contentType,
  contentId,
  contentTitle,
  onSuccess
}: FlagContentModalProps) {
  const user = useStore(userStore);
  const [selectedReason, setSelectedReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('You must be logged in to flag content');
      return;
    }

    if (!selectedReason) {
      setError('Please select a reason for flagging this content');
      return;
    }

    if (selectedReason === 'Other' && !customReason.trim()) {
      setError('Please provide a reason for flagging this content');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const reason = selectedReason === 'Other' ? customReason.trim() : selectedReason;
      
      // This would normally submit to PocketBase
      const response = await fetch('/api/flag-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content_type: contentType,
          content_id: contentId,
          reason: reason,
          reporter: user.id
        })
      });

      if (!response.ok) {
        throw new Error('Failed to submit flag');
      }

      // Success
      onSuccess?.();
      onClose();
      
      // Reset form
      setSelectedReason('');
      setCustomReason('');
      
    } catch (err) {
      console.error('Flag submission error:', err);
      setError('Failed to submit flag. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedReason('');
      setCustomReason('');
      setError('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-[#E5E7EB]">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-[#1F2937]">Flag Content</h3>
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-[#6B7280] hover:text-[#1F2937] transition-colors duration-300 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {contentTitle && (
            <p className="text-sm text-[#6B7280] mt-2">
              Reporting: <span className="font-medium">{contentTitle}</span>
            </p>
          )}
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <p className="text-sm text-[#6B7280] mb-4">
              Help us maintain a safe and trustworthy platform by reporting content that violates our community guidelines.
            </p>
          </div>

          {/* Reason Selection */}
          <div>
            <label className="block text-sm font-medium text-[#1F2937] mb-3">
              Why are you flagging this content? *
            </label>
            <div className="space-y-2">
              {FLAG_REASONS.map((reason) => (
                <label key={reason} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="reason"
                    value={reason}
                    checked={selectedReason === reason}
                    onChange={(e) => setSelectedReason(e.target.value)}
                    className="h-4 w-4 text-[#059669] border-[#E5E7EB] focus:ring-[#059669]"
                    disabled={isSubmitting}
                  />
                  <span className="text-sm text-[#1F2937]">{reason}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Custom Reason */}
          {selectedReason === 'Other' && (
            <div>
              <label htmlFor="customReason" className="block text-sm font-medium text-[#1F2937] mb-2">
                Please specify the reason *
              </label>
              <textarea
                id="customReason"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                rows={3}
                className="w-full px-4 py-3 border border-[#E5E7EB] rounded-lg bg-white text-base transition-all duration-300 ease-in-out focus:outline-none focus:ring-3 focus:ring-[#059669]/20 focus:border-[#059669] resize-none"
                placeholder="Please provide details about why you're flagging this content..."
                disabled={isSubmitting}
                maxLength={500}
              />
              <p className="text-xs text-[#6B7280] mt-1">
                {customReason.length}/500 characters
              </p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Disclaimer */}
          <div className="p-4 bg-[#F9FAFB] border border-[#E5E7EB] rounded-lg">
            <p className="text-xs text-[#6B7280]">
              <strong>Note:</strong> False or malicious reports may result in action against your account. 
              Our team will review all flagged content and take appropriate action if violations are found.
            </p>
          </div>

          {/* Actions */}
          <div className="flex space-x-4 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 py-3 px-6 border-2 border-[#E5E7EB] text-[#6B7280] font-medium rounded-lg hover:bg-[#F9FAFB] transition-all duration-300 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !selectedReason || (selectedReason === 'Other' && !customReason.trim())}
              className="flex-1 flex justify-center items-center py-3 px-6 bg-red-600 text-white font-bold rounded-lg hover:bg-red-700 focus:outline-none focus:ring-3 focus:ring-red-600/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </>
              ) : (
                'Submit Flag'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
