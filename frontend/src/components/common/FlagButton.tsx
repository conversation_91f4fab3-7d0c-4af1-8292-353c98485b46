import { useState } from 'react';
import FlagContentModal from './FlagContentModal';

interface FlagButtonProps {
  contentType: 'venue' | 'user_profile';
  contentId: string;
  contentTitle?: string;
  className?: string;
  variant?: 'icon' | 'button' | 'text';
  size?: 'sm' | 'md' | 'lg';
  onFlagSubmitted?: () => void;
}

export default function FlagButton({
  contentType,
  contentId,
  contentTitle,
  className = '',
  variant = 'icon',
  size = 'md',
  onFlagSubmitted
}: FlagButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleFlagSubmitted = () => {
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 3000);
    onFlagSubmitted?.();
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  const getButtonClasses = () => {
    const baseClasses = 'transition-colors duration-300 focus:outline-none';
    
    switch (variant) {
      case 'button':
        return `${baseClasses} inline-flex items-center px-3 py-2 border border-red-300 text-red-600 bg-white rounded-lg hover:bg-red-50 focus:ring-2 focus:ring-red-500 focus:ring-offset-2`;
      case 'text':
        return `${baseClasses} text-red-600 hover:text-red-700 underline`;
      default:
        return `${baseClasses} text-[#6B7280] hover:text-red-600`;
    }
  };

  if (showSuccess) {
    return (
      <div className="inline-flex items-center text-green-600">
        <svg className={`${getSizeClasses()} mr-1`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
        {variant !== 'icon' && <span className="text-sm">Flagged</span>}
      </div>
    );
  }

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={`${getButtonClasses()} ${className}`}
        title="Flag this content"
        aria-label="Flag this content"
      >
        <svg className={getSizeClasses()} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 2H21l-3 6 3 6h-8.5l-1-2H5a2 2 0 00-2 2zm9-13.5V9" 
          />
        </svg>
        {variant === 'button' && <span className="ml-2 text-sm">Flag</span>}
        {variant === 'text' && <span className="ml-1">Flag this content</span>}
      </button>

      <FlagContentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        contentType={contentType}
        contentId={contentId}
        contentTitle={contentTitle}
        onSuccess={handleFlagSubmitted}
      />
    </>
  );
}
