import { useState, useCallback } from 'react';
import { Plus, X, GripVertical } from 'lucide-react';

interface ChecklistItem {
  id: string;
  text: string;
}

interface ChecklistEditorProps {
  initialItems?: ChecklistItem[];
  onChange: (items: ChecklistItem[]) => void;
  disabled?: boolean;
}

export default function ChecklistEditor({ 
  initialItems = [], 
  onChange, 
  disabled = false 
}: ChecklistEditorProps) {
  const [items, setItems] = useState<ChecklistItem[]>(
    initialItems.length > 0 
      ? initialItems 
      : [{ id: generateId(), text: '' }]
  );

  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  const updateItems = useCallback((newItems: ChecklistItem[]) => {
    setItems(newItems);
    onChange(newItems.filter(item => item.text.trim() !== ''));
  }, [onChange]);

  const addItem = () => {
    const newItems = [...items, { id: generateId(), text: '' }];
    updateItems(newItems);
  };

  const removeItem = (id: string) => {
    if (items.length <= 1) return; // Keep at least one item
    const newItems = items.filter(item => item.id !== id);
    updateItems(newItems);
  };

  const updateItemText = (id: string, text: string) => {
    const newItems = items.map(item => 
      item.id === id ? { ...item, text } : item
    );
    updateItems(newItems);
  };

  const handleKeyDown = (e: React.KeyboardEvent, id: string, index: number) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // Add new item after current one
      const newItem = { id: generateId(), text: '' };
      const newItems = [
        ...items.slice(0, index + 1),
        newItem,
        ...items.slice(index + 1)
      ];
      updateItems(newItems);
      
      // Focus the new input after a brief delay
      setTimeout(() => {
        const newInput = document.querySelector(`input[data-id="${newItem.id}"]`) as HTMLInputElement;
        if (newInput) {
          newInput.focus();
        }
      }, 50);
    } else if (e.key === 'Backspace' && items[index].text === '' && items.length > 1) {
      e.preventDefault();
      removeItem(id);
      
      // Focus previous input if available
      if (index > 0) {
        setTimeout(() => {
          const prevInput = document.querySelector(`input[data-id="${items[index - 1].id}"]`) as HTMLInputElement;
          if (prevInput) {
            prevInput.focus();
            prevInput.setSelectionRange(prevInput.value.length, prevInput.value.length);
          }
        }, 50);
      }
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-slate-900">
          Move-out Checklist
        </label>
        <button
          type="button"
          onClick={addItem}
          disabled={disabled}
          className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-primary-green bg-primary-greenSubtle hover:bg-primary-greenMuted rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Plus className="w-3 h-3 mr-1" />
          Add Item
        </button>
      </div>

      <p className="text-sm text-slate-600">
        Create a checklist that renters must complete before checking out. This helps ensure your venue is left in good condition.
      </p>

      <div className="space-y-2">
        {items.map((item, index) => (
          <div
            key={item.id}
            className="group flex items-center space-x-3 p-3 bg-slate-50 rounded-lg border border-slate-200 hover:border-slate-300 transition-all duration-200"
          >
            <div className="flex-shrink-0 text-slate-400 cursor-grab">
              <GripVertical className="w-4 h-4" />
            </div>

            <div className="flex-1">
              <input
                type="text"
                data-id={item.id}
                value={item.text}
                onChange={(e) => updateItemText(item.id, e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, item.id, index)}
                disabled={disabled}
                className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:ring-2 focus:ring-primary-green focus:border-primary-green disabled:bg-slate-100 disabled:cursor-not-allowed"
                placeholder={`Checklist item ${index + 1}...`}
              />
            </div>

            {items.length > 1 && (
              <button
                type="button"
                onClick={() => removeItem(item.id)}
                disabled={disabled}
                className="flex-shrink-0 p-1 text-slate-400 hover:text-red-500 transition-colors duration-200 opacity-0 group-hover:opacity-100 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Remove item"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        ))}
      </div>

      {items.filter(item => item.text.trim() !== '').length === 0 && (
        <div className="text-center py-4 text-sm text-slate-500">
          Add checklist items to help renters prepare for checkout
        </div>
      )}

      <div className="text-xs text-slate-500 mt-2">
        <p>• Press Enter to add a new item</p>
        <p>• Press Backspace on an empty item to remove it</p>
        <p>• Items will be shown to renters during their final day</p>
      </div>
    </div>
  );
}
