import { useState } from 'react';
import { X, Mail, Send, Users, CheckCircle } from 'lucide-react';
import Modal from '../common/Modal.tsx';

interface InvitationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { message: string; emails: string[] }) => Promise<void>;
  bookingDetails: {
    venueName: string;
    startDate: string;
    endDate: string;
    renterName: string;
  };
  isLoading?: boolean;
}

export default function InvitationForm({ 
  isOpen, 
  onClose, 
  onSubmit, 
  bookingDetails, 
  isLoading = false 
}: InvitationFormProps) {
  const [customMessage, setCustomMessage] = useState('');
  const [emailList, setEmailList] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [invitationCount, setInvitationCount] = useState(0);

  const parseEmails = (emailText: string): string[] => {
    return emailText
      .split(/[\n,;]/)
      .map(email => email.trim())
      .filter(email => email && isValidEmail(email));
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const getDefaultMessage = (): string => {
    const startDate = new Date(bookingDetails.startDate).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    const startTime = new Date(bookingDetails.startDate).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    return `Hi there!

You're invited to join me at ${bookingDetails.venueName} on ${startDate} at ${startTime}.

Looking forward to seeing you there!

Best regards,
${bookingDetails.renterName}`;
  };

  const validEmails = parseEmails(emailList);
  const invalidEmails = emailList
    .split(/[\n,;]/)
    .map(email => email.trim())
    .filter(email => email && !isValidEmail(email));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validEmails.length === 0 || isLoading) return;

    try {
      const message = customMessage.trim() || getDefaultMessage();
      await onSubmit({ message, emails: validEmails });
      
      setInvitationCount(validEmails.length);
      setShowSuccess(true);
      
      // Reset form after successful submission
      setTimeout(() => {
        setCustomMessage('');
        setEmailList('');
        setShowSuccess(false);
        onClose();
      }, 3000);
    } catch (error) {
      console.error('Failed to send invitations:', error);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setCustomMessage('');
      setEmailList('');
      setShowSuccess(false);
      onClose();
    }
  };

  if (showSuccess) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose} title="">
        <div className="text-center py-8">
          <CheckCircle className="w-16 h-16 text-primary-green mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-slate-900 mb-2">
            Invitations Sent!
          </h3>
          <p className="text-slate-600 mb-4">
            {invitationCount} invitation{invitationCount !== 1 ? 's have' : ' has'} been sent successfully.
          </p>
          <p className="text-sm text-slate-500">
            This window will close automatically in a few seconds.
          </p>
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Invite Guests">
      <div className="space-y-6">
        {/* Event details */}
        <div className="bg-slate-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-900 mb-2 flex items-center">
            <Users className="w-4 h-4 mr-2" />
            Event Details
          </h4>
          <div className="text-sm text-slate-600 space-y-1">
            <p><strong>Venue:</strong> {bookingDetails.venueName}</p>
            <p><strong>Date:</strong> {new Date(bookingDetails.startDate).toLocaleDateString()}</p>
            <p><strong>Time:</strong> {new Date(bookingDetails.startDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Custom message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-slate-900 mb-2">
              Custom Message (Optional)
            </label>
            <textarea
              id="message"
              rows={6}
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              disabled={isLoading}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green disabled:bg-slate-100 disabled:cursor-not-allowed"
              placeholder={getDefaultMessage()}
            />
            <p className="mt-1 text-xs text-slate-500">
              Leave blank to use the default invitation message
            </p>
          </div>

          {/* Email list */}
          <div>
            <label htmlFor="emails" className="block text-sm font-medium text-slate-900 mb-2">
              Guest Email Addresses *
            </label>
            <textarea
              id="emails"
              rows={4}
              value={emailList}
              onChange={(e) => setEmailList(e.target.value)}
              disabled={isLoading}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green disabled:bg-slate-100 disabled:cursor-not-allowed"
              placeholder="Enter email addresses, one per line or separated by commas:&#10;<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
            />
            
            {/* Email validation feedback */}
            <div className="mt-2 space-y-1">
              {validEmails.length > 0 && (
                <p className="text-xs text-primary-green flex items-center">
                  <Mail className="w-3 h-3 mr-1" />
                  {validEmails.length} valid email{validEmails.length !== 1 ? 's' : ''} found
                </p>
              )}
              {invalidEmails.length > 0 && (
                <p className="text-xs text-red-600">
                  Invalid emails: {invalidEmails.join(', ')}
                </p>
              )}
            </div>
          </div>

          {/* Submit button */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={validEmails.length === 0 || isLoading}
              className={`px-4 py-2 text-sm font-medium rounded-lg flex items-center transition-all duration-300 ${
                validEmails.length > 0 && !isLoading
                  ? 'bg-primary-green text-white hover:bg-primary-greenDark hover:scale-[1.02] shadow-medium hover:shadow-large'
                  : 'bg-slate-300 text-slate-500 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle 
                      className="opacity-25" 
                      cx="12" 
                      cy="12" 
                      r="10" 
                      stroke="currentColor" 
                      strokeWidth="4"
                    />
                    <path 
                      className="opacity-75" 
                      fill="currentColor" 
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send {validEmails.length} Invitation{validEmails.length !== 1 ? 's' : ''}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
