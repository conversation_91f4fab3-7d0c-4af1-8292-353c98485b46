import { useState, useEffect } from 'react';
import { CheckCircle, Circle, Send } from 'lucide-react';
import type { ChecklistItem } from '../../types/venue.ts';

interface ChecklistFormData {
  [itemId: string]: boolean;
}

interface ChecklistFormProps {
  items: ChecklistItem[];
  onSubmit: (data: ChecklistFormData, notes?: string) => Promise<void>;
  isLoading?: boolean;
  disabled?: boolean;
}

export default function ChecklistForm({ 
  items, 
  onSubmit, 
  isLoading = false, 
  disabled = false 
}: ChecklistFormProps) {
  const [checkedItems, setCheckedItems] = useState<ChecklistFormData>({});
  const [notes, setNotes] = useState('');
  const [allItemsChecked, setAllItemsChecked] = useState(false);

  // Initialize checked items state
  useEffect(() => {
    const initialState: ChecklistFormData = {};
    items.forEach(item => {
      initialState[item.id] = false;
    });
    setCheckedItems(initialState);
  }, [items]);

  // Check if all items are completed
  useEffect(() => {
    const allChecked = items.length > 0 && items.every(item => checkedItems[item.id] === true);
    setAllItemsChecked(allChecked);
  }, [checkedItems, items]);

  const handleItemToggle = (itemId: string) => {
    if (disabled) return;
    
    setCheckedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!allItemsChecked || isLoading || disabled) return;
    
    try {
      await onSubmit(checkedItems, notes.trim() || undefined);
    } catch (error) {
      console.error('Failed to submit checklist:', error);
    }
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-8 text-slate-500">
        <p>No checklist items have been set for this venue.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-card border border-slate-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-2">
          Move-out Checklist
        </h3>
        <p className="text-sm text-slate-600">
          Please complete all items before checking out. This helps ensure the venue is left in good condition for the next guest.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-3">
          {items.map((item, index) => {
            const isChecked = checkedItems[item.id] || false;
            
            return (
              <div
                key={item.id}
                className={`group flex items-start space-x-3 p-4 rounded-lg border transition-all duration-300 ${
                  isChecked 
                    ? 'bg-primary-greenSubtle border-primary-green' 
                    : 'bg-slate-50 border-slate-200 hover:border-slate-300'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                onClick={() => handleItemToggle(item.id)}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {isChecked ? (
                    <CheckCircle 
                      className="w-5 h-5 text-primary-green animate-star-pop" 
                    />
                  ) : (
                    <Circle 
                      className="w-5 h-5 text-slate-400 group-hover:text-slate-600 transition-colors duration-200" 
                    />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className={`text-sm font-medium transition-all duration-300 ${
                    isChecked 
                      ? 'text-primary-green line-through' 
                      : 'text-slate-900'
                  }`}>
                    {item.text}
                  </p>
                </div>

                <div className="flex-shrink-0 text-xs text-slate-500 font-medium">
                  {index + 1}
                </div>
              </div>
            );
          })}
        </div>

        {/* Progress indicator */}
        <div className="mt-6 p-4 bg-slate-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-700">
              Progress
            </span>
            <span className="text-sm text-slate-600">
              {Object.values(checkedItems).filter(Boolean).length} of {items.length} completed
            </span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-2">
            <div 
              className="bg-primary-green h-2 rounded-full transition-all duration-500 ease-out"
              style={{ 
                width: `${(Object.values(checkedItems).filter(Boolean).length / items.length) * 100}%` 
              }}
            />
          </div>
        </div>

        {/* Optional notes */}
        <div className="mt-6">
          <label htmlFor="notes" className="block text-sm font-medium text-slate-700 mb-2">
            Additional Notes (Optional)
          </label>
          <textarea
            id="notes"
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            disabled={disabled || isLoading}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green disabled:bg-slate-100 disabled:cursor-not-allowed"
            placeholder="Any additional comments or notes for the venue owner..."
          />
        </div>

        {/* Submit button */}
        <div className="mt-6 pt-4 border-t border-slate-200">
          <button
            type="submit"
            disabled={!allItemsChecked || isLoading || disabled}
            className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-all duration-300 ${
              allItemsChecked && !isLoading && !disabled
                ? 'bg-primary-green text-white hover:bg-primary-greenDark hover:scale-[1.02] shadow-medium hover:shadow-large'
                : 'bg-slate-300 text-slate-500 cursor-not-allowed'
            }`}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                  <circle 
                    className="opacity-25" 
                    cx="12" 
                    cy="12" 
                    r="10" 
                    stroke="currentColor" 
                    strokeWidth="4"
                  />
                  <path 
                    className="opacity-75" 
                    fill="currentColor" 
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Submitting...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Submit to Owner
              </>
            )}
          </button>
          
          {!allItemsChecked && (
            <p className="mt-2 text-xs text-slate-500 text-center">
              Complete all checklist items to submit
            </p>
          )}
        </div>
      </form>
    </div>
  );
}
