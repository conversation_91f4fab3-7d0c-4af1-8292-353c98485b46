import { CheckCircle, MessageSquare, Calendar, User } from 'lucide-react';
import type { ChecklistItem } from '../../types/venue.ts';

interface ChecklistSubmissionData {
  id: string;
  booking: string;
  renter: {
    id: string;
    name: string;
    email: string;
  };
  submission_data: Record<string, boolean>;
  notes?: string;
  created: string;
  updated: string;
}

interface ChecklistSubmissionProps {
  submission: ChecklistSubmissionData;
  checklistItems: ChecklistItem[];
  className?: string;
}

export default function ChecklistSubmission({ 
  submission, 
  checklistItems, 
  className = '' 
}: ChecklistSubmissionProps) {
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCompletedItems = (): ChecklistItem[] => {
    return checklistItems.filter(item => submission.submission_data[item.id] === true);
  };

  const getIncompleteItems = (): ChecklistItem[] => {
    return checklistItems.filter(item => submission.submission_data[item.id] !== true);
  };

  const completedItems = getCompletedItems();
  const incompleteItems = getIncompleteItems();
  const completionRate = checklistItems.length > 0 
    ? (completedItems.length / checklistItems.length) * 100 
    : 0;

  return (
    <div className={`bg-white rounded-xl shadow-card border border-slate-200 p-6 ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-900">
            Checklist Submission
          </h3>
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            completionRate === 100 
              ? 'bg-primary-greenSubtle text-primary-green' 
              : 'bg-secondary-yellowSubtle text-secondary-yellow'
          }`}>
            {Math.round(completionRate)}% Complete
          </div>
        </div>

        {/* Submission metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-600">
          <div className="flex items-center space-x-2">
            <User className="w-4 h-4" />
            <span>Submitted by: {submission.renter.name}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4" />
            <span>Submitted: {formatDate(submission.created)}</span>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-slate-700">
            Overall Progress
          </span>
          <span className="text-sm text-slate-600">
            {completedItems.length} of {checklistItems.length} items
          </span>
        </div>
        <div className="w-full bg-slate-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${
              completionRate === 100 ? 'bg-primary-green' : 'bg-secondary-yellow'
            }`}
            style={{ width: `${completionRate}%` }}
          />
        </div>
      </div>

      {/* Completed items */}
      {completedItems.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-primary-green mb-3 flex items-center">
            <CheckCircle className="w-4 h-4 mr-2" />
            Completed Items ({completedItems.length})
          </h4>
          <div className="space-y-2">
            {completedItems.map((item, index) => (
              <div
                key={item.id}
                className="flex items-center space-x-3 p-3 bg-primary-greenSubtle rounded-lg"
              >
                <CheckCircle className="w-4 h-4 text-primary-green flex-shrink-0" />
                <span className="text-sm text-primary-green line-through flex-1">
                  {item.text}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Incomplete items */}
      {incompleteItems.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-secondary-yellow mb-3 flex items-center">
            <div className="w-4 h-4 mr-2 border-2 border-secondary-yellow rounded-full" />
            Incomplete Items ({incompleteItems.length})
          </h4>
          <div className="space-y-2">
            {incompleteItems.map((item, index) => (
              <div
                key={item.id}
                className="flex items-center space-x-3 p-3 bg-secondary-yellowSubtle rounded-lg"
              >
                <div className="w-4 h-4 border-2 border-secondary-yellow rounded-full flex-shrink-0" />
                <span className="text-sm text-secondary-yellow flex-1">
                  {item.text}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Notes */}
      {submission.notes && (
        <div className="border-t border-slate-200 pt-4">
          <h4 className="text-sm font-medium text-slate-700 mb-2 flex items-center">
            <MessageSquare className="w-4 h-4 mr-2" />
            Additional Notes
          </h4>
          <div className="bg-slate-50 rounded-lg p-3">
            <p className="text-sm text-slate-700 whitespace-pre-wrap">
              {submission.notes}
            </p>
          </div>
        </div>
      )}

      {/* Summary for owners */}
      <div className="mt-6 p-4 bg-slate-50 rounded-lg">
        <div className="text-center">
          {completionRate === 100 ? (
            <div className="text-primary-green">
              <CheckCircle className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm font-medium">All checklist items completed!</p>
              <p className="text-xs text-slate-600 mt-1">
                The renter has completed all required checkout tasks.
              </p>
            </div>
          ) : (
            <div className="text-secondary-yellow">
              <div className="w-8 h-8 mx-auto mb-2 border-3 border-secondary-yellow rounded-full flex items-center justify-center">
                <span className="text-xs font-bold">{Math.round(completionRate)}%</span>
              </div>
              <p className="text-sm font-medium">Partial completion</p>
              <p className="text-xs text-slate-600 mt-1">
                {incompleteItems.length} item{incompleteItems.length !== 1 ? 's' : ''} still need{incompleteItems.length === 1 ? 's' : ''} attention.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
