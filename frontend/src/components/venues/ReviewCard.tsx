import React, { useState } from 'react';
import StarRating from '../common/StarRating.tsx';
import Button from '../common/Button.tsx';
import { addOwnerResponse } from '../../lib/pocketbase.ts';
import type { ReviewWithExpanded } from '../../types/review.ts';

interface ReviewCardProps {
  review: ReviewWithExpanded;
  currentUserId?: string;
  isVenueOwner?: boolean;
  onResponseAdded?: () => void;
}

export default function ReviewCard({ 
  review, 
  currentUserId, 
  isVenueOwner = false,
  onResponseAdded 
}: ReviewCardProps) {
  const [showResponseForm, setShowResponseForm] = useState(false);
  const [responseText, setResponseText] = useState('');
  const [isSubmittingResponse, setIsSubmittingResponse] = useState(false);
  const [responseError, setResponseError] = useState('');

  const renter = review.expand?.renter;
  const createdDate = new Date(review.created).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const handleResponseSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (responseText.trim().length < 10) {
      setResponseError('Response must be at least 10 characters long');
      return;
    }

    setIsSubmittingResponse(true);
    setResponseError('');

    try {
      const result = await addOwnerResponse(review.id, {
        owner_response: responseText.trim()
      });

      if (result.success) {
        setShowResponseForm(false);
        setResponseText('');
        if (onResponseAdded) {
          onResponseAdded();
        }
      } else {
        setResponseError(result.error || 'Failed to submit response');
      }
    } catch (err) {
      setResponseError('An unexpected error occurred');
      console.error('Response submission error:', err);
    } finally {
      setIsSubmittingResponse(false);
    }
  };

  const canRespond = isVenueOwner && 
                    currentUserId && 
                    (!review.owner_response || review.owner_response.trim() === '');

  return (
    <div className="bg-white rounded-xl border border-slate-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
      {/* Reviewer Info */}
      <div className="flex items-start gap-4 mb-4">
        <div className="flex-shrink-0">
          {renter?.avatar ? (
            <img
              src={renter.avatar}
              alt={renter.name || 'Reviewer'}
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
              <span className="text-primary-600 font-semibold text-lg">
                {(renter?.name || 'Anonymous').charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-slate-900 truncate">
              {renter?.name || 'Anonymous'}
            </h4>
            <time className="text-sm text-slate-500 flex-shrink-0">
              {createdDate}
            </time>
          </div>
          
          <div className="flex items-center gap-2 mb-3">
            <StarRating
              rating={review.rating}
              readonly
              size="sm"
              showValue={false}
            />
            <span className="text-sm font-medium text-slate-700">
              {review.rating}/5
            </span>
          </div>
        </div>
      </div>

      {/* Review Comment */}
      <div className="mb-4">
        <p className="text-slate-700 leading-relaxed whitespace-pre-wrap">
          {review.comment}
        </p>
      </div>

      {/* Owner Response */}
      {review.owner_response && review.owner_response.trim() !== '' && (
        <div className="mt-4 pl-4 border-l-3 border-primary-200 bg-primary-50 rounded-r-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-semibold text-primary-700">
              Response from the owner:
            </span>
          </div>
          <p className="text-slate-700 text-sm leading-relaxed whitespace-pre-wrap">
            {review.owner_response}
          </p>
        </div>
      )}

      {/* Owner Response Form */}
      {canRespond && !showResponseForm && (
        <div className="mt-4 pt-4 border-t border-slate-200">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowResponseForm(true)}
            className="text-sm"
          >
            Respond to this review
          </Button>
        </div>
      )}

      {showResponseForm && (
        <div className="mt-4 pt-4 border-t border-slate-200">
          <form onSubmit={handleResponseSubmit} className="space-y-3">
            <div>
              <label htmlFor={`response-${review.id}`} className="block text-sm font-medium text-slate-700 mb-2">
                Your response
              </label>
              <textarea
                id={`response-${review.id}`}
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                placeholder="Thank your guest and address any concerns..."
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none text-sm"
                disabled={isSubmittingResponse}
                maxLength={500}
              />
              <div className="flex justify-between mt-1">
                <span className="text-xs text-slate-500">
                  Minimum 10 characters
                </span>
                <span className="text-xs text-slate-500">
                  {responseText.length}/500
                </span>
              </div>
            </div>

            {responseError && (
              <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                {responseError}
              </div>
            )}

            <div className="flex gap-2">
              <Button
                type="button"
                variant="secondary"
                size="sm"
                onClick={() => {
                  setShowResponseForm(false);
                  setResponseText('');
                  setResponseError('');
                }}
                disabled={isSubmittingResponse}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                size="sm"
                disabled={isSubmittingResponse || responseText.trim().length < 10}
              >
                {isSubmittingResponse ? 'Submitting...' : 'Submit Response'}
              </Button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
