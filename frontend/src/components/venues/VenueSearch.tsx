import { useState, useEffect, useCallback, useRef } from 'react';
import { Search, MapPin, Filter, X } from 'lucide-react';
import type { Venue, VenueSearchFilters } from '../../types/venue.ts';
import VenueCard, { VenueCardSkeleton } from './VenueCard.tsx';
import { searchVenues } from '../../lib/meilisearch.js';

interface VenueSearchProps {
  onVenueSelect?: (venue: Venue) => void;
  initialQuery?: string;
  className?: string;
}

interface SearchState {
  query: string;
  results: Venue[];
  isLoading: boolean;
  hasSearched: boolean;
  totalResults: number;
  processingTime: number;
  error?: string;
}

const DEBOUNCE_DELAY = 200; // Reduced for more responsive feel
const SKELETON_COUNT = 6;

export default function VenueSearch({ 
  onVenueSelect, 
  initialQuery = '', 
  className = '' 
}: VenueSearchProps) {
  const [searchState, setSearchState] = useState<SearchState>({
    query: initialQuery,
    results: [],
    isLoading: false,
    hasSearched: false,
    totalResults: 0,
    processingTime: 0
  });

  const [filters, setFilters] = useState<VenueSearchFilters>({
    query: initialQuery,
    sort: 'relevance',
    limit: 12
  });

  const [showFilters, setShowFilters] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  
  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout>>();
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Debounced search function
  const performSearch = useCallback(async (searchQuery: string, searchFilters: VenueSearchFilters) => {
    if (!searchQuery.trim() && !searchFilters.minPrice && !searchFilters.maxPrice && !searchFilters.minCapacity) {
      setSearchState(prev => ({
        ...prev,
        results: [],
        hasSearched: false,
        isLoading: false
      }));
      return;
    }

    setSearchState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const searchOptions = {
        limit: searchFilters.limit || 12,
        offset: ((searchFilters.page || 1) - 1) * (searchFilters.limit || 12),
        filters: buildFilterString(searchFilters),
        sort: buildSortArray(searchFilters.sort || 'relevance')
      };

      const response = await searchVenues(searchQuery, searchOptions);

      if (response.success) {
        setSearchState(prev => ({
          ...prev,
          results: response.hits,
          totalResults: response.totalHits,
          processingTime: response.processingTimeMs,
          isLoading: false,
          hasSearched: true
        }));
      } else {
        setSearchState(prev => ({
          ...prev,
          error: response.error || 'Search failed',
          isLoading: false,
          hasSearched: true
        }));
      }
    } catch (_error) {
      setSearchState(prev => ({
        ...prev,
        error: 'Search service unavailable',
        isLoading: false,
        hasSearched: true
      }));
    }
  }, []);

  // Handle search input changes with debouncing
  const handleSearchChange = useCallback((value: string) => {
    setSearchState(prev => ({ ...prev, query: value }));
    setFilters(prev => ({ ...prev, query: value }));

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      performSearch(value, { ...filters, query: value });
    }, DEBOUNCE_DELAY);
  }, [filters, performSearch]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<VenueSearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    performSearch(searchState.query, updatedFilters);
  }, [filters, searchState.query, performSearch]);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchState({
      query: '',
      results: [],
      isLoading: false,
      hasSearched: false,
      totalResults: 0,
      processingTime: 0
    });
    setFilters({ query: '', sort: 'relevance', limit: 12 });
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Build filter string for MeiliSearch
  const buildFilterString = (searchFilters: VenueSearchFilters): string => {
    const filterParts: string[] = ['is_published = true'];

    if (searchFilters.minPrice) {
      filterParts.push(`price_per_hour >= ${searchFilters.minPrice}`);
    }
    if (searchFilters.maxPrice) {
      filterParts.push(`price_per_hour <= ${searchFilters.maxPrice}`);
    }
    if (searchFilters.minCapacity) {
      filterParts.push(`capacity >= ${searchFilters.minCapacity}`);
    }
    if (searchFilters.amenities && searchFilters.amenities.length > 0) {
      const amenityFilters = searchFilters.amenities.map(amenity => `amenities = "${amenity}"`);
      filterParts.push(`(${amenityFilters.join(' OR ')})`);
    }

    return filterParts.join(' AND ');
  };

  // Build sort array for MeiliSearch
  const buildSortArray = (sortOption: string): string[] => {
    switch (sortOption) {
      case 'price_asc':
        return ['price_per_hour:asc'];
      case 'price_desc':
        return ['price_per_hour:desc'];
      case 'capacity_asc':
        return ['capacity:asc'];
      case 'capacity_desc':
        return ['capacity:desc'];
      case 'rating':
        return ['average_rating:desc'];
      case 'newest':
        return ['created:desc'];
      default:
        return [];
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle initial query
  useEffect(() => {
    if (initialQuery) {
      performSearch(initialQuery, filters);
    }
  }, [initialQuery, filters, performSearch]);

  return (
    <div className={`w-full ${className}`}>
      {/* Search Bar */}
      <div className="relative mb-6">
        <div className={`
          relative bg-white rounded-xl shadow-medium transition-all duration-300
          ${isFocused ? 'shadow-large ring-2 ring-primary-green ring-opacity-20' : ''}
        `}>
          <div className="flex items-center">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-primary-green w-5 h-5" />
            <input
              ref={searchInputRef}
              type="text"
              value={searchState.query}
              onChange={(e) => handleSearchChange(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder="Search for a venue by city, name, or type..."
              className="w-full pl-12 pr-20 py-4 text-lg border-none rounded-xl focus:outline-none focus:ring-0"
            />
            
            {/* Clear button */}
            {searchState.query && (
              <button
                type="button"
                onClick={clearSearch}
                className="absolute right-12 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            )}

            {/* Filter button */}
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className={`
                absolute right-4 top-1/2 transform -translate-y-1/2 p-2 rounded-lg transition-colors
                ${showFilters ? 'text-primary-green bg-primary-greenSubtle' : 'text-slate-400 hover:text-slate-600'}
              `}
            >
              <Filter className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Search Stats */}
        {searchState.hasSearched && !searchState.isLoading && (
          <div className="mt-3 flex items-center justify-between text-sm text-slate-600">
            <span>
              {searchState.totalResults} venue{searchState.totalResults !== 1 ? 's' : ''} found
              {searchState.processingTime > 0 && ` in ${searchState.processingTime}ms`}
            </span>
            {searchState.query && (
              <span>for "{searchState.query}"</span>
            )}
          </div>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="mb-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Min Price per Hour
              </label>
              <input
                type="number"
                min="0"
                value={filters.minPrice || ''}
                onChange={(e) => handleFilterChange({ minPrice: parseFloat(e.target.value) || undefined })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green"
                placeholder="$0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Max Price per Hour
              </label>
              <input
                type="number"
                min="0"
                value={filters.maxPrice || ''}
                onChange={(e) => handleFilterChange({ maxPrice: parseFloat(e.target.value) || undefined })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green"
                placeholder="$1000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Min Capacity
              </label>
              <input
                type="number"
                min="1"
                value={filters.minCapacity || ''}
                onChange={(e) => handleFilterChange({ minCapacity: parseInt(e.target.value) || undefined })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green"
                placeholder="1"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Sort by
            </label>
            <select
              value={filters.sort || 'relevance'}
              onChange={(e) => handleFilterChange({ sort: e.target.value as VenueSearchFilters['sort'] })}
              className="w-full md:w-auto px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-primary-green"
            >
              <option value="relevance">Relevance</option>
              <option value="price_asc">Price: Low to High</option>
              <option value="price_desc">Price: High to Low</option>
              <option value="capacity_asc">Capacity: Small to Large</option>
              <option value="capacity_desc">Capacity: Large to Small</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest</option>
            </select>
          </div>
        </div>
      )}

      {/* Error State */}
      {searchState.error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{searchState.error}</p>
        </div>
      )}

      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {searchState.isLoading ? (
          // Enhanced skeleton loading with staggered animation
          Array.from({ length: SKELETON_COUNT }).map((_, index) => (
            <div
              key={index}
              className="animate-pulse"
              style={{
                animationDelay: `${index * 100}ms`,
                animationDuration: '1.5s'
              }}
            >
              <VenueCardSkeleton />
            </div>
          ))
        ) : searchState.hasSearched && searchState.results.length === 0 ? (
          // No results
          <div className="col-span-full text-center py-12">
            <MapPin className="w-16 h-16 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">No venues found</h3>
            <p className="text-slate-600">
              Try adjusting your search terms or filters to find more venues.
            </p>
          </div>
        ) : (
          // Search results with staggered fade-in animation
          searchState.results.map((venue, index) => (
            <div
              key={venue.id}
              className="opacity-0 animate-fadeInUp"
              style={{
                animationDelay: `${index * 150}ms`,
                animationFillMode: 'forwards'
              }}
            >
              <VenueCard
                venue={venue}
                onViewDetails={onVenueSelect ? (venueId: string) => {
                  const venue = searchState.results.find(v => v.id === venueId);
                  if (venue) onVenueSelect(venue);
                } : undefined}
                className="transform hover:scale-105 transition-all duration-300 hover:shadow-xl"
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
}
