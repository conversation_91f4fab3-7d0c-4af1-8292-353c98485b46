// React import not needed for modern React with JSX transform
import type { Venue } from '../../types/venue.ts';
import { CompactStarRating } from '../common/StarRating.tsx';
import Button from '../common/Button.tsx';

interface VenueCardProps {
  venue: Venue;
  onBookNow?: (venueId: string) => void;
  onViewDetails?: (venueId: string) => void;
  showActions?: boolean;
  className?: string;
}

export default function VenueCard({ 
  venue, 
  onBookNow, 
  onViewDetails,
  showActions = true,
  className = '' 
}: VenueCardProps) {
  const handleBookNow = () => {
    if (onBookNow) {
      onBookNow(venue.id);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(venue.id);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(price);
  };

  return (
    <div className={`bg-white rounded-xl shadow-card overflow-hidden hover:shadow-large transition-all duration-300 ${className}`}>
      {/* Image */}
      <div className="relative h-48 bg-slate-200">
        {venue.standard_photos && venue.standard_photos.length > 0 ? (
          <img
            src={venue.standard_photos[0]}
            alt={venue.title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-slate-400">
            <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}

        {/* 360° Badge */}
        {venue.pano_photo && (
          <div className="absolute top-3 left-3">
            <span className="bg-primary-green text-white text-xs font-medium px-2 py-1 rounded-full shadow-medium">
              360° View
            </span>
          </div>
        )}

        {/* Price Badge */}
        <div className="absolute top-3 right-3">
          <span className="bg-white text-slate-900 text-sm font-semibold px-3 py-1 rounded-full shadow-medium">
            {formatPrice(venue.price_per_hour)}/hr
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="mb-2">
          <h3 className="text-xl font-semibold text-slate-900 line-clamp-1 mb-1">
            {venue.title}
          </h3>
          <p className="text-sm text-slate-600 line-clamp-1">
            {venue.address ? `${venue.address.street}, ${venue.address.city}, ${venue.address.country}` : 'Address not available'}
          </p>
        </div>

        <p className="text-sm text-slate-700 line-clamp-2 mb-4">
          {venue.description}
        </p>

        {/* Amenities */}
        {venue.amenities && venue.amenities.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {venue.amenities.slice(0, 3).map((amenity, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-greenSubtle text-primary-green"
                >
                  {amenity}
                </span>
              ))}
              {venue.amenities.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-700">
                  +{venue.amenities.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Capacity */}
        <div className="flex items-center text-sm text-slate-600 mb-4">
          <svg className="w-4 h-4 mr-2 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          Up to {venue.capacity} guests
        </div>

        {/* Rating */}
        {venue.average_rating && (
          <div className="mb-4">
            <CompactStarRating 
              rating={venue.average_rating} 
              reviewCount={venue.review_count}
            />
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              className="flex-1"
            >
              View Details
            </Button>
            <Button
              size="sm"
              onClick={handleBookNow}
              className="flex-1"
            >
              Book Now
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

// Enhanced skeleton loader that perfectly mimics VenueCard layout
export function VenueCardSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-card overflow-hidden">
      {/* Image skeleton */}
      <div className="relative h-48 bg-slate-200 animate-pulse">
        <div className="absolute inset-0 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 animate-shimmer"></div>
      </div>

      {/* Content skeleton */}
      <div className="p-6">
        {/* Title skeleton */}
        <div className="h-6 bg-slate-200 rounded-lg w-3/4 mb-3 animate-pulse"></div>

        {/* Location skeleton */}
        <div className="flex items-center mb-4">
          <div className="w-4 h-4 bg-slate-200 rounded mr-2 animate-pulse"></div>
          <div className="h-4 bg-slate-200 rounded w-1/2 animate-pulse"></div>
        </div>

        {/* Description skeleton */}
        <div className="space-y-2 mb-4">
          <div className="h-3 bg-slate-200 rounded w-full animate-pulse"></div>
          <div className="h-3 bg-slate-200 rounded w-4/5 animate-pulse"></div>
        </div>

        {/* Amenities skeleton */}
        <div className="flex flex-wrap gap-2 mb-4">
          <div className="h-6 bg-slate-200 rounded-full w-16 animate-pulse"></div>
          <div className="h-6 bg-slate-200 rounded-full w-20 animate-pulse"></div>
          <div className="h-6 bg-slate-200 rounded-full w-14 animate-pulse"></div>
        </div>

        {/* Capacity skeleton */}
        <div className="flex items-center mb-4">
          <div className="w-4 h-4 bg-slate-200 rounded mr-2 animate-pulse"></div>
          <div className="h-4 bg-slate-200 rounded w-24 animate-pulse"></div>
        </div>

        {/* Rating skeleton */}
        <div className="flex items-center mb-6">
          <div className="flex gap-1 mr-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="w-4 h-4 bg-slate-200 rounded animate-pulse"></div>
            ))}
          </div>
          <div className="h-4 bg-slate-200 rounded w-16 animate-pulse"></div>
        </div>

        {/* Price and button skeleton */}
        <div className="flex items-center justify-between">
          <div>
            <div className="h-6 bg-slate-200 rounded w-20 mb-1 animate-pulse"></div>
            <div className="h-3 bg-slate-200 rounded w-12 animate-pulse"></div>
          </div>
          <div className="h-10 bg-slate-200 rounded-lg w-24 animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
