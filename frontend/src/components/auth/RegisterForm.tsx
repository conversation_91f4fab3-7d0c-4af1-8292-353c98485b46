"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Eye, EyeOff, CheckCircle, AlertCircle, Loader2 } from "lucide-react"

interface FormData {
  name: string
  email: string
  password: string
  passwordConfirm: string
  agreeToTerms: boolean
}

interface PasswordStrength {
  score: number
  feedback: string[]
  color: string
  label: string
}

export default function RegisterPage() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    passwordConfirm: "",
    agreeToTerms: false,
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const getPasswordStrength = (password: string): PasswordStrength => {
    let score = 0
    const feedback = []

    if (password.length >= 8) score += 1
    else feedback.push("At least 8 characters")

    if (/(?=.*[a-z])/.test(password)) score += 1
    else feedback.push("One lowercase letter")

    if (/(?=.*[A-Z])/.test(password)) score += 1
    else feedback.push("One uppercase letter")

    if (/(?=.*\d)/.test(password)) score += 1
    else feedback.push("One number")

    if (/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) score += 1
    else feedback.push("One special character")

    let color = "#DC2626" // red
    let label = "Weak"
    if (score >= 4) {
      color = "#059669" // primary green
      label = "Strong"
    } else if (score >= 3) {
      color = "#F59E0B" // secondary yellow
      label = "Good"
    }

    return { score, feedback, color, label }
  }

  const passwordStrength = getPasswordStrength(formData.password)

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return "Name is required"
    if (!formData.email) return "Email is required"
    if (!/\S+@\S+\.\S+/.test(formData.email)) return "Please enter a valid email address"
    if (passwordStrength.score < 3) return "Password must meet the requirements"
    if (formData.password !== formData.passwordConfirm) return "Passwords do not match"
    if (!formData.agreeToTerms) return "You must agree to the Terms of Service"
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Simulate success - in real app, handle actual registration
      console.log("Registration successful:", formData)
      // window.location.href = '/dashboard?welcome=true'
    } catch (_err) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const updateFormData = (field: keyof FormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    if (error) setError("") // Clear error when user starts typing
  }

  return (
    <div className="min-h-screen flex" style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}>
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 opacity-30 pointer-events-none">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(5, 150, 105, 0.1) 1px, transparent 0)`,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Left Column - Desktop Only */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden bg-gradient-to-br from-emerald-600 to-emerald-500">
        {/* GridMotion Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `radial-gradient(circle at 25px 25px, rgba(5, 150, 105, 0.3) 2px, transparent 0), radial-gradient(circle at 75px 75px, rgba(5, 150, 105, 0.3) 2px, transparent 0)`,
              backgroundSize: "100px 100px",
            }}
          />
        </div>

        {/* Glassmorphism Overlay */}
        <div className="absolute inset-0 bg-white/10 backdrop-blur-2xl" />

        {/* Floating Gradient Orbs */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-emerald-400/20 to-emerald-600/20 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 rounded-full blur-3xl" />

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center px-12 py-16">
          <div className="max-w-md">
            {/* Logo with Back Button */}
            <div className="flex items-center justify-between mb-8">
              <a href="/" className="inline-block transition-all duration-300 hover:scale-105">
                <h1
                  className="text-4xl font-bold text-white tracking-wide"
                  style={{ fontFamily: "'Anton', sans-serif" }}
                >
                  Trodoo
                </h1>
              </a>
              <Button
                variant="ghost"
                size="sm"
                className="bg-white/20 backdrop-blur-md border border-white/30 text-white hover:bg-white/30 rounded-2xl px-4 py-2 transition-all duration-300 hover:scale-105"
                onClick={() => globalThis.history?.back()}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </div>

            {/* Tagline */}
            <h2
              className="text-4xl font-bold text-white mb-6 leading-tight"
              style={{ fontFamily: "'Poppins', 'Helvetica Neue', Arial, sans-serif" }}
            >
              Start Your Journey
            </h2>

            <p className="text-xl text-white/90 mb-8 leading-relaxed font-normal">
              Join thousands of event organizers who trust Trodoo to find the perfect venues for their special
              occasions.
            </p>

            {/* Benefits */}
            <div className="space-y-4">
              {[
                "Access to premium venues nationwide",
                "Secure booking and payment processing",
                "24/7 customer support",
                "List your own venues and earn",
              ].map((benefit, index) => (
                <div key={index} className="flex items-center space-x-3 group">
                  <div className="w-2 h-2 bg-white rounded-full group-hover:scale-125 transition-transform duration-300" />
                  <span className="text-white/90 text-base">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Decorative GridMotion Pattern */}
        <div className="absolute bottom-0 right-0 w-64 h-64 opacity-20">
          <svg viewBox="0 0 200 200" className="w-full h-full">
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(5, 150, 105, 0.5)" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="200" height="200" fill="url(#grid)" />
          </svg>
        </div>
      </div>

      {/* Right Column - Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12 bg-gradient-to-br from-slate-50 to-white relative">
        <div className="max-w-md w-full space-y-8">
          {/* Mobile Logo with Back Button */}
          <div className="lg:hidden">
            <div className="flex items-center justify-between mb-4">
              <Button
                variant="outline"
                size="sm"
                className="bg-slate-100 border-slate-200 text-emerald-600 hover:bg-slate-200 hover:border-slate-300 rounded-lg px-3 py-2 transition-all duration-300 hover:scale-105"
                onClick={() => globalThis.history?.back()}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <a href="/" className="inline-block transition-all duration-300 hover:scale-105">
                <h1
                  className="text-3xl font-bold text-emerald-600 tracking-wide"
                  style={{ fontFamily: "'Anton', sans-serif" }}
                >
                  Trodoo
                </h1>
              </a>
              <div className="w-16" /> {/* Spacer for centering */}
            </div>
          </div>

          {/* Header */}
          <div className="text-center">
            <h2
              className="text-4xl font-bold leading-tight text-slate-900"
              style={{ fontFamily: "'Poppins', 'Helvetica Neue', Arial, sans-serif" }}
            >
              Create Your Account
            </h2>
            <p className="mt-3 text-base leading-relaxed text-slate-600">
              Already have an account?{" "}
              <a
                href="/auth/login"
                className="font-medium text-emerald-600 hover:text-emerald-700 transition-colors duration-300 hover:scale-105 inline-block"
              >
                Sign In
              </a>
            </p>
          </div>

          {/* Registration Form Container */}
          <div className="bg-white rounded-2xl p-8 border border-slate-200 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name Field */}
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium mb-2 text-slate-700"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                >
                  Full Name
                </label>
                <Input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData("name", e.target.value)}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg bg-white text-base transition-all duration-300 focus:border-emerald-600 focus:ring-2 focus:ring-emerald-600/20 disabled:opacity-50"
                  placeholder="Enter your full name"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                />
              </div>

              {/* Email Field */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium mb-2 text-slate-700"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                >
                  Email Address
                </label>
                <Input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={(e) => updateFormData("email", e.target.value)}
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg bg-white text-base transition-all duration-300 focus:border-emerald-600 focus:ring-2 focus:ring-emerald-600/20 disabled:opacity-50"
                  placeholder="Enter your email"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                />
              </div>

              {/* Password Field */}
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium mb-2 text-slate-700"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                >
                  Password
                </label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    value={formData.password}
                    onChange={(e) => updateFormData("password", e.target.value)}
                    required
                    disabled={isLoading}
                    className="w-full px-4 py-3 pr-12 border border-slate-300 rounded-lg bg-white text-base transition-all duration-300 focus:border-emerald-600 focus:ring-2 focus:ring-emerald-600/20 disabled:opacity-50"
                    placeholder="Create a password"
                    style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-slate-400 hover:text-emerald-600 transition-colors duration-300"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </Button>
                </div>

                {/* Password Strength Indicator */}
                {formData.password && (
                  <div className="mt-3 space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 rounded-full h-2 bg-slate-200">
                        <div
                          className="h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${(passwordStrength.score / 5) * 100}%`,
                            backgroundColor: passwordStrength.color,
                          }}
                        />
                      </div>
                      <span
                        className="text-xs font-medium"
                        style={{
                          color: passwordStrength.color,
                          fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif",
                        }}
                      >
                        {passwordStrength.label}
                      </span>
                    </div>
                    {passwordStrength.feedback.length > 0 && (
                      <div
                        className="text-xs text-slate-600"
                        style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                      >
                        <p className="mb-1">Password must include:</p>
                        <ul className="space-y-1">
                          {passwordStrength.feedback.map((item, index) => (
                            <li key={index} className="flex items-center space-x-2">
                              <span className="w-1 h-1 rounded-full bg-slate-400" />
                              <span>{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Confirm Password Field */}
              <div>
                <label
                  htmlFor="passwordConfirm"
                  className="block text-sm font-medium mb-2 text-slate-700"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                >
                  Confirm Password
                </label>
                <div className="relative">
                  <Input
                    type={showPasswordConfirm ? "text" : "password"}
                    id="passwordConfirm"
                    value={formData.passwordConfirm}
                    onChange={(e) => updateFormData("passwordConfirm", e.target.value)}
                    required
                    disabled={isLoading}
                    className="w-full px-4 py-3 pr-12 border border-slate-300 rounded-lg bg-white text-base transition-all duration-300 focus:border-emerald-600 focus:ring-2 focus:ring-emerald-600/20 disabled:opacity-50"
                    placeholder="Confirm your password"
                    style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-slate-400 hover:text-emerald-600 transition-colors duration-300"
                    onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
                    disabled={isLoading}
                  >
                    {showPasswordConfirm ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </Button>
                </div>
              </div>

              {/* Terms Agreement */}
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => updateFormData("agreeToTerms", checked as boolean)}
                  disabled={isLoading}
                  className="mt-1 data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
                />
                <label
                  htmlFor="agreeToTerms"
                  className="text-sm text-slate-600"
                  style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                >
                  I agree to the{" "}
                  <a
                    href="/terms"
                    className="font-medium text-emerald-600 hover:text-emerald-700 transition-colors duration-300"
                  >
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a
                    href="/privacy"
                    className="font-medium text-emerald-600 hover:text-emerald-700 transition-colors duration-300"
                  >
                    Privacy Policy
                  </a>
                </label>
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-4 rounded-lg bg-red-50 border border-red-200 animate-in fade-in duration-300">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-red-600" />
                    <p
                      className="text-sm text-red-600"
                      style={{ fontFamily: "'Roboto', 'Helvetica Neue', Arial, sans-serif" }}
                    >
                      {error}
                    </p>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || !formData.agreeToTerms}
                className="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold text-base py-3 px-6 rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                style={{ fontFamily: "'Poppins', 'Helvetica Neue', Arial, sans-serif" }}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Creating account...
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Create Account
                  </>
                )}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
