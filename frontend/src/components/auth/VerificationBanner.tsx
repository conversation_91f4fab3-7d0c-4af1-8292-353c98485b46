import { useState, useEffect } from 'react';
import { X, Mail, CheckCircle } from 'lucide-react';

interface VerificationBannerProps {
  isVisible: boolean;
  onDismiss: () => void;
  onResendEmail: () => Promise<void>;
  userEmail?: string;
}

export default function VerificationBanner({ 
  isVisible, 
  onDismiss, 
  onResendEmail, 
  userEmail 
}: VerificationBannerProps) {
  const [isResending, setIsResending] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [lastResendTime, setLastResendTime] = useState<number | null>(null);
  const [cooldownRemaining, setCooldownRemaining] = useState(0);

  // Rate limiting: 60 seconds between resend attempts
  const RESEND_COOLDOWN = 60000; // 60 seconds

  useEffect(() => {
    if (lastResendTime && cooldownRemaining > 0) {
      const timer = setInterval(() => {
        const timeElapsed = Date.now() - lastResendTime;
        const remaining = Math.max(0, RESEND_COOLDOWN - timeElapsed);
        setCooldownRemaining(remaining);
        
        if (remaining === 0) {
          clearInterval(timer);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [lastResendTime, cooldownRemaining]);

  const handleResendEmail = async () => {
    if (cooldownRemaining > 0) return;

    setIsResending(true);
    try {
      await onResendEmail();
      setLastResendTime(Date.now());
      setCooldownRemaining(RESEND_COOLDOWN);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Failed to resend verification email:', error);
    } finally {
      setIsResending(false);
    }
  };

  const formatCooldownTime = (ms: number): string => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}s`;
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 animate-slide-down">
      <div className="bg-secondary-yellow border-b border-secondary-yellowDark px-4 py-3 shadow-medium">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Mail className="w-5 h-5 text-secondary-yellowDark flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm font-medium text-neutral-black">
                Please check your email to verify your account.
                {userEmail && (
                  <span className="text-secondary-yellowDark ml-1">
                    ({userEmail})
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {showSuccess ? (
              <div className="flex items-center space-x-2 text-primary-green">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">Sent!</span>
              </div>
            ) : (
              <button
                onClick={handleResendEmail}
                disabled={isResending || cooldownRemaining > 0}
                className={`text-sm font-medium px-3 py-1.5 rounded-md transition-all duration-300 ${
                  isResending || cooldownRemaining > 0
                    ? 'bg-neutral-mediumGray text-neutral-darkGray cursor-not-allowed'
                    : 'bg-neutral-black text-neutral-white hover:bg-neutral-charcoal hover:scale-105'
                }`}
              >
                {isResending ? (
                  <div className="flex items-center space-x-2">
                    <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                      <circle 
                        className="opacity-25" 
                        cx="12" 
                        cy="12" 
                        r="10" 
                        stroke="currentColor" 
                        strokeWidth="4"
                      />
                      <path 
                        className="opacity-75" 
                        fill="currentColor" 
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    <span>Sending...</span>
                  </div>
                ) : cooldownRemaining > 0 ? (
                  `Resend in ${formatCooldownTime(cooldownRemaining)}`
                ) : (
                  'Resend Email'
                )}
              </button>
            )}

            <button
              onClick={onDismiss}
              className="text-neutral-darkGray hover:text-neutral-black transition-colors duration-200 p-1"
              aria-label="Dismiss verification banner"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
