import { Search, Shield, MessageCircle, Sparkles } from "lucide-react"
import SpotlightTiltCard from "../SpotlightTiltCard/SpotlightTiltCard.tsx"
import { motion } from "framer-motion"

const features = [
  {
    icon: Search,
    title: "Easy Search & Discovery",
    description:
      "Find the perfect venue with our advanced search filters. Browse by location, capacity, amenities, and more.",
    gradient: "from-primary-400 to-primary-500",
    spotlightColor: "rgba(16, 185, 129, 0.3)",
    bgGradient: "from-primary-50 to-primary-100",
  },
  {
    icon: Shield,
    title: "Secure Payments",
    description:
      "Book with confidence using our secure payment system. Protected transactions for both renters and owners.",
    gradient: "from-secondary-400 to-secondary-500",
    spotlightColor: "rgba(245, 158, 11, 0.3)",
    bgGradient: "from-secondary-50 to-secondary-100",
  },
  {
    icon: MessageCircle,
    title: "Direct Communication",
    description:
      "Connect directly with property owners through our in-app messaging system. No intermediaries, just direct communication.",
    gradient: "from-slate-400 to-slate-500",
    spotlightColor: "rgba(100, 116, 139, 0.3)",
    bgGradient: "from-slate-50 to-slate-100",
  },
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut" as const,
    },
  },
}

export default function FeaturesSection() {
  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background with enhanced gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100" />
      <div className="absolute inset-0 bg-gradient-to-tr from-emerald-50/30 via-transparent to-blue-50/30" />

      {/* Decorative elements */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-emerald-200/20 to-teal-200/20 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-blue-200/20 to-indigo-200/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-amber-100/10 to-orange-100/10 rounded-full blur-3xl" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-primary-100 to-primary-200 text-primary-700 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            Why Choose Trodoo
          </div>
          <h2 className="text-display bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 bg-clip-text text-transparent mb-6 leading-tight">
            All Venue Details You Need
            <br />
            <span className="bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent">
              In One Platform
            </span>
          </h2>
          <p className="text-body-large max-w-3xl mx-auto leading-relaxed">
            We provide an all-in-one platform that makes venue rental simple, secure, and transparent. Experience the
            future of event planning.
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div key={index} variants={itemVariants}>
                <SpotlightTiltCard className="h-[400px] group" spotlightColor={feature.spotlightColor}>
                  {/* Icon with enhanced styling */}
                  <div
                    className={`relative w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${feature.gradient} p-0.5 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <div className="w-full h-full rounded-2xl bg-white flex items-center justify-center">
                      <Icon className="w-10 h-10 text-slate-700" />
                    </div>
                    {/* Glow effect */}
                    <div
                      className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300`}
                    />
                  </div>

                  {/* Content */}
                  <div className="space-y-4">
                    <h3 className="text-heading-3 group-hover:text-slate-800 transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-body leading-relaxed">{feature.description}</p>
                  </div>

                  {/* Decorative bottom accent */}
                  <div
                    className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                  />
                </SpotlightTiltCard>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Call to action */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-4 px-8 py-4 rounded-full bg-gradient-to-r from-primary-600 to-primary-500 text-white font-semibold text-lg shadow-medium hover:shadow-large hover:scale-105 transition-all duration-300 cursor-pointer">
            Get Started Today
            <div className="w-2 h-2 rounded-full bg-white animate-pulse" />
          </div>
        </motion.div>
      </div>
    </section>
  )
}
