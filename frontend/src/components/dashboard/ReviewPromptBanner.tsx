import React, { useState, useEffect } from 'react';
import { getEligibleBookingsForReview, getCurrentUser } from '../../lib/pocketbase';
import ReviewForm from '../venues/ReviewForm';
import Button from '../common/Button';
import { X, Star } from 'lucide-react';
import type { ReviewPrompt } from '../../types/review';

interface ReviewPromptBannerProps {
  className?: string;
}

export default function ReviewPromptBanner({ className = '' }: ReviewPromptBannerProps) {
  const [prompts, setPrompts] = useState<ReviewPrompt[]>([]);
  const [dismissedPrompts, setDismissedPrompts] = useState<Set<string>>(new Set());
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<ReviewPrompt | null>(null);
  const [loading, setLoading] = useState(true);

  const currentUser = getCurrentUser();

  const loadPrompts = async () => {
    if (!currentUser) {
      setLoading(false);
      return;
    }

    try {
      const result = await getEligibleBookingsForReview(currentUser.id);
      if (result.success && result.prompts) {
        setPrompts(result.prompts);
      }
    } catch (error) {
      console.error('Failed to load review prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDismiss = (bookingId: string) => {
    setDismissedPrompts(prev => new Set([...prev, bookingId]));
    // Store dismissed prompts in localStorage
    const dismissed = Array.from(dismissedPrompts);
    dismissed.push(bookingId);
    localStorage.setItem('dismissedReviewPrompts', JSON.stringify(dismissed));
  };

  const handleLeaveReview = (prompt: ReviewPrompt) => {
    setSelectedPrompt(prompt);
    setShowReviewForm(true);
  };

  const handleReviewSubmitted = () => {
    // Remove the prompt from the list and mark as dismissed
    if (selectedPrompt) {
      handleDismiss(selectedPrompt.bookingId);
      setSelectedPrompt(null);
    }
    setShowReviewForm(false);
    // Reload prompts to get updated list
    loadPrompts();
  };

  useEffect(() => {
    // Load dismissed prompts from localStorage
    const dismissed = localStorage.getItem('dismissedReviewPrompts');
    if (dismissed) {
      try {
        const dismissedArray = JSON.parse(dismissed);
        setDismissedPrompts(new Set(dismissedArray));
      } catch (error) {
        console.error('Failed to parse dismissed prompts:', error);
      }
    }

    loadPrompts();
  }, [currentUser]);

  // Filter out dismissed prompts
  const visiblePrompts = prompts.filter(prompt => !dismissedPrompts.has(prompt.bookingId));

  if (loading || !currentUser || visiblePrompts.length === 0) {
    return null;
  }

  return (
    <>
      <div className={`space-y-3 ${className}`}>
        {visiblePrompts.map((prompt, index) => (
          <div
            key={prompt.bookingId}
            className="bg-gradient-to-r from-primary-600 to-primary-500 text-white rounded-xl p-4 shadow-lg animate-slide-down"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Star className="w-5 h-5 text-white" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-white mb-1">
                    How was your experience?
                  </h3>
                  <p className="text-primary-100 text-sm">
                    Leave a review for <span className="font-medium text-white">{prompt.venueName}</span>
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2 flex-shrink-0">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleLeaveReview(prompt)}
                  className="bg-white text-primary-600 hover:bg-primary-50 border-0 font-medium"
                >
                  Leave Review
                </Button>
                
                <button
                  onClick={() => handleDismiss(prompt.bookingId)}
                  className="p-1 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors duration-200"
                  aria-label="Dismiss"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Review Form Modal */}
      {showReviewForm && selectedPrompt && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => {
            setShowReviewForm(false);
            setSelectedPrompt(null);
          }}
          bookingId={selectedPrompt.bookingId}
          venueId={selectedPrompt.venueId}
          venueName={selectedPrompt.venueName}
          renterId={currentUser.id}
          onReviewSubmitted={handleReviewSubmitted}
        />
      )}
    </>
  );
}
