@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  body {
    @apply bg-neutral-white text-neutral-black;
    color: #1F2937; /* neutral.black - softer than pure black */
    background-color: #FFFFFF; /* neutral.white */
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  /* Base Button Styles */
  .btn {
    @apply inline-flex items-center justify-center gap-2 font-medium transition-all duration-300 ease-out;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  /* Primary Button - Amber/Yellow */
  .btn-primary {
    @apply bg-secondary-500 text-neutral-black font-bold text-base px-6 py-3 rounded-full shadow-medium;
    @apply hover:bg-secondary-600 hover:scale-[1.02] hover:shadow-large;
    @apply focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-opacity-20;
    @apply transition-all duration-300 ease-out;
  }

  /* Secondary Button - Transparent with border */
  .btn-secondary {
    @apply bg-transparent text-white border-2 border-white font-semibold px-5 py-2.5 rounded-full;
    @apply hover:bg-white hover:text-primary-600 hover:scale-[1.02];
    @apply focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-20;
    @apply transition-all duration-300 ease-out;
  }

  /* Glassmorphism Button */
  .btn-glassmorphism {
    @apply bg-white/20 backdrop-blur-xl text-white border border-white/30 px-6 py-3 rounded-xl;
    @apply hover:bg-white/30;
    @apply focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-20;
    @apply transition-all duration-300 ease-out;
  }

  /* Card Styles */
  .card {
    @apply bg-white rounded-xl shadow-card border border-slate-200 p-8;
    @apply hover:shadow-large hover:-translate-y-0.5;
    @apply transition-all duration-300 ease-out;
  }

  .card-spotlight {
    @apply bg-white rounded-xl shadow-card border border-slate-200 p-8;
    @apply hover:shadow-large hover:-translate-y-0.5;
    @apply transition-all duration-300 ease-out;
  }

  .card-glassmorphism {
    @apply bg-white/10 backdrop-blur-2xl rounded-2xl border border-white/20 shadow-glassmorphism;
  }

  /* Input Styles */
  .input {
    @apply w-full px-4 py-3 border border-slate-300 rounded-lg bg-white text-base;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-opacity-10 focus:border-primary-600;
    @apply transition-all duration-300 ease-out;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  .input-error {
    @apply border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-opacity-10 focus:border-red-500;
  }

  .input-glassmorphism {
    @apply bg-transparent border-none text-white placeholder-white/80 px-4 py-3 text-base;
    @apply focus:outline-none focus:ring-0;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  /* Label Styles */
  .label {
    @apply text-sm font-medium text-slate-700 mb-2 block;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  /* Typography Utilities */
  .text-hero {
    @apply text-6xl font-extrabold leading-tight;
    font-family: 'Anton', sans-serif;
  }

  .text-display {
    @apply text-5xl font-bold leading-tight;
    font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
  }

  .text-heading-1 {
    @apply text-3xl font-bold leading-tight text-slate-900;
    font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
  }

  .text-heading-2 {
    @apply text-2xl font-bold leading-tight text-slate-900;
    font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
  }

  .text-heading-3 {
    @apply text-xl font-semibold leading-tight text-slate-900;
    font-family: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
  }

  .text-body-large {
    @apply text-xl leading-relaxed text-slate-600;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  .text-body {
    @apply text-base leading-relaxed text-slate-600;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  .text-body-small {
    @apply text-sm leading-relaxed text-slate-700;
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  }

  /* Gradient Text */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-500 bg-clip-text text-transparent;
  }
}

@layer utilities {
  /* Custom animations for the authentication forms */
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  @keyframes fade-in {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
  }

  @keyframes star-pop {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
  }

  @keyframes slide-down {
    0% { opacity: 0; transform: translateY(-20px); }
    100% { opacity: 1; transform: translateY(0); }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-in-out;
  }

  .animate-star-pop {
    animation: star-pop 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .animate-slide-down {
    animation: slide-down 0.4s ease-out;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
