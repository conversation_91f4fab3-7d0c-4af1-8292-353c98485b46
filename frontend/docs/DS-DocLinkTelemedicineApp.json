{"designSystem": {"name": "DocLink Telemedicine App Design System", "version": "1.0.0", "description": "A comprehensive design system for the DocLink Telemedicine App, aiming for a patient-centered, professional, and plush aesthetic with a deep blue-black, white, gold, and orange theme. Inspired by modern, clean, and engaging UI/UX patterns.", "branding": {"appName": "DocLink", "logoGuidelines": "Abstract representation of connectivity or health, utilizing primary brand colors. Should be easily recognizable and scalable."}, "colorPalette": {"primary": {"deepBlueBlack": "#1A1A2E", "darkNavy": "#0A0A2A"}, "secondary": {"goldAccent": "#FFD700", "orangeAccent": "#FFA500"}, "neutrals": {"white": "#FFFFFF", "offWhite": "#F8F8F8", "lightGray": "#CCCCCC", "darkGray": "#555555"}, "feedback": {"success": "#28A745", "warning": "#FFC107", "error": "#DC3545", "info": "#17A2B8"}}, "typography": {"fontFamily": "'<PERSON>o', 'Arial', sans-serif", "headings": {"h1": {"fontSize": "36px", "fontWeight": "700", "color": "primary.deepBlueBlack"}, "h2": {"fontSize": "28px", "fontWeight": "600", "color": "primary.deepBlueBlack"}, "h3": {"fontSize": "24px", "fontWeight": "500", "color": "primary.deepBlueBlack"}, "h4": {"fontSize": "20px", "fontWeight": "500", "color": "primary.deepBlueBlack"}, "h5": {"fontSize": "18px", "fontWeight": "500", "color": "primary.deepBlueBlack"}, "h6": {"fontSize": "16px", "fontWeight": "500", "color": "primary.deepBlueBlack"}}, "bodyText": {"large": {"fontSize": "16px", "fontWeight": "400", "color": "neutrals.dark<PERSON>ray"}, "medium": {"fontSize": "14px", "fontWeight": "400", "color": "neutrals.dark<PERSON>ray"}, "small": {"fontSize": "12px", "fontWeight": "400", "color": "neutrals.dark<PERSON>ray"}}, "callToAction": {"fontSize": "16px", "fontWeight": "600"}, "caption": {"fontSize": "10px", "fontWeight": "400", "color": "neutrals.lightGray"}}, "spacing": {"unit": "8px", "xxs": "4px", "xs": "8px", "sm": "16px", "md": "24px", "lg": "32px", "xl": "48px", "xxl": "64px"}, "borders": {"radius": {"none": "0px", "sm": "4px", "md": "8px", "lg": "12px", "pill": "999px"}, "width": {"thin": "1px", "medium": "2px", "thick": "3px"}, "color": {"default": "neutrals.lightGray", "active": "secondary.goldAccent", "error": "feedback.error"}}, "shadows": {"none": "none", "sm": "0px 2px 4px rgba(0, 0, 0, 0.1)", "md": "0px 4px 8px rgba(0, 0, 0, 0.15)", "lg": "0px 8px 16px rgba(0, 0, 0, 0.2)"}, "buttons": {"primary": {"backgroundColor": "secondary.goldAccent", "textColor": "primary.deepBlueBlack", "borderRadius": "borders.radius.md", "padding": "spacing.sm spacing.md", "fontWeight": "typography.callToAction.fontWeight", "fontSize": "typography.callToAction.fontSize", "hover": {"backgroundColor": "orangeAccent", "shadow": "shadows.sm"}, "active": {"backgroundColor": "secondary.orangeAccent", "shadow": "shadows.md"}}, "secondary": {"backgroundColor": "primary.darkNavy", "textColor": "neutrals.white", "borderRadius": "borders.radius.md", "padding": "spacing.sm spacing.md", "fontWeight": "typography.callToAction.fontWeight", "fontSize": "typography.callToAction.fontSize", "hover": {"backgroundColor": "#1A1A3A", "shadow": "shadows.sm"}, "active": {"backgroundColor": "#050515", "shadow": "shadows.md"}}, "outline": {"backgroundColor": "transparent", "borderColor": "secondary.goldAccent", "borderWidth": "borders.width.thin", "textColor": "secondary.goldAccent", "borderRadius": "borders.radius.md", "padding": "spacing.sm spacing.md", "fontWeight": "typography.callToAction.fontWeight", "fontSize": "typography.callToAction.fontSize", "hover": {"backgroundColor": "rgba(255, 215, 0, 0.1)", "borderColor": "secondary.orangeAccent", "textColor": "secondary.orangeAccent"}}, "text": {"backgroundColor": "transparent", "textColor": "primary.deepBlueBlack", "padding": "spacing.xs", "fontWeight": "typography.bodyText.medium.fontWeight", "fontSize": "typography.bodyText.medium.fontSize", "hover": {"textColor": "secondary.goldAccent"}}}, "forms": {"inputField": {"backgroundColor": "neutrals.white", "borderColor": "neutrals.lightGray", "borderWidth": "borders.width.thin", "borderRadius": "borders.radius.sm", "padding": "spacing.xs spacing.sm", "textColor": "neutrals.dark<PERSON>ray", "placeholderColor": "neutrals.lightGray", "focus": {"borderColor": "secondary.goldAccent", "shadow": "0px 0px 0px 2px rgba(255, 215, 0, 0.2)"}, "error": {"borderColor": "feedback.error", "shadow": "0px 0px 0px 2px rgba(220, 53, 69, 0.2)"}}, "label": {"textColor": "primary.deepBlueBlack", "fontSize": "typography.bodyText.medium.fontSize", "fontWeight": "typography.bodyText.medium.fontWeight"}, "checkboxRadio": {"size": "20px", "borderColor": "neutrals.lightGray", "checkedColor": "secondary.goldAccent", "uncheckedColor": "neutrals.white", "checkmarkColor": "neutrals.white"}}, "navigation": {"header": {"backgroundColor": "primary.deepBlueBlack", "textColor": "neutrals.white", "height": "60px", "linkHoverColor": "secondary.goldAccent"}, "footer": {"backgroundColor": "primary.darkNavy", "textColor": "neutrals.white", "padding": "spacing.md spacing.lg"}, "tabs": {"backgroundColor": "neutrals.white", "activeTabColor": "primary.deepBlueBlack", "inactiveTabColor": "neutrals.dark<PERSON>ray", "indicatorColor": "secondary.goldAccent", "padding": "spacing.xs spacing.sm", "borderRadius": "borders.radius.md"}, "sidebar": {"backgroundColor": "neutrals.white", "textColor": "primary.deepBlueBlack", "activeItemBg": "rgba(255, 215, 0, 0.1)", "activeItemColor": "secondary.goldAccent"}}, "icons": {"style": "Plush, intuitive, and modern. Should complement the overall aesthetic. Material Design or Font Awesome Pro (if licensed) with customizations for brand colors are good references.", "color": "primary.deepBlueBlack", "hoverColor": "secondary.goldAccent", "size": "24px"}, "illustrations": {"style": "Clean, simple, and vector-based. Should avoid overly complex details and focus on conveying information clearly. Utilize brand colors. Examples: abstract medical elements, people interacting with technology in a friendly manner."}, "imagery": {"style": "High-quality, professional photography of healthcare professionals and patients, always depicting a diverse and inclusive representation. Images should convey trust, professionalism, and empathy. Soft focus or depth of field can be used to add a 'plush' feel.", "treatment": "Subtle color grading to align with the overall palette. Avoid overly saturated or cartoonish images."}, "animations": {"duration": "0.3s", "easing": "ease-in-out", "types": ["fade-in-out", "slide-in", "subtle hover effects", "loading indicators"]}, "microInteractions": {"general": "Subtle visual feedback on user actions (button presses, input focus).", "buttons": "Slight scale on press, color change on hover.", "icons": "Subtle bounce or color change on tap/hover.", "formElements": "Clear focus states, validation feedback."}, "accessibility": {"colorContrastRatio": "WCAG AA or AAA for all text and interactive elements.", "fontSizing": "Scalable fonts, user-adjustable.", "altText": "All images and meaningful graphics require descriptive alt text.", "keyboardNavigation": "All interactive elements navigable via keyboard.", "screenReaderSupport": "Semantic HTML/components for screen reader compatibility.", "languageSelection": "Prominent language selector (English, Twi) at onboarding."}, "responsiveDesign": {"breakpoints": {"mobile": "320px - 767px", "tablet": "768px - 1023px", "desktop": "1024px and up"}, "fluidLayout": "Grid-based layouts that adapt to screen size.", "typographyScaling": "Font sizes adjust for readability on different devices."}, "componentLibrary": {"buttons": ["Primary", "Secondary", "Outline", "Text"], "inputFields": ["Text Input", "Password Input", "Dropdown", "Checkbox", "Radio Button", "Text Area"], "cards": ["Information Card", "Profile Card", "Appointment Card"], "navigation": ["Header (App Bar)", "Bottom Navigation Bar (Mobile)", "Sidebar (Tablet/Desktop)", "Tabs"], "modals": ["<PERSON><PERSON>", "Confirmation Modal", "Information Modal"], "loaders": ["Spinners", "Progress Bars", "Skeletons"], "avatars": ["User Avatar", "Doctor <PERSON><PERSON>"], "badges": ["Notification Badge", "Status Badge"], "sliders": ["Image Slider", "Range Slider"], "toasts": ["Success Toast", "<PERSON><PERSON><PERSON>", "Info Toast"], "tooltips": [], "typographyElements": ["Headings", "Body Text", "Captions", "Links"]}, "layoutStructure": {"general": "White backgrounds for app content areas. Dark navy/deep blue-black for main headers, footers, and prominent sections (e.g., hero sections). Gold/orange accents for interactive elements and key information.", "onboardingScreens": "Clean, simple layouts with clear calls to action. Prominent language selector. Visually appealing illustrations/imagery related to health and accessibility.", "teleconsultationScreens": "Clear video/audio call interface. Easy access to chat, notes, and e-prescription. Minimal distractions.", "dashboardScreens": "Well-organized data display using cards and clear headings. Focus on readability and quick access to key information.", "dataDisplay": "Use cards for individual items (e.g., past consultations, medications). Tables for structured data where appropriate (e.g., transaction history).", "kioskIntegration": "Ensure seamless visual and functional transition between app and kiosk UI. Consistent branding and interaction patterns."}}}