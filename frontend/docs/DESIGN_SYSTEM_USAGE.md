# Trodoo Vibe Design System - Usage Guide

This guide shows how to use the Trodoo Vibe Design System components and utilities across the application.

## Overview

The design system is defined in `docs/design.json` and implemented through:
- **Global CSS**: `src/styles/global.css` - Component classes and utilities
- **Tailwind Config**: `tailwind.config.mjs` - Extended theme and utilities
- **CSS Variables**: `src/layouts/BaseLayout.astro` - Design tokens

## Color System

### Primary Colors (Green)
```css
/* CSS Classes */
.bg-primary-50    /* #ECFDF5 - Lightest */
.bg-primary-100   /* #D1FAE5 */
.bg-primary-500   /* #10B981 */
.bg-primary-600   /* #059669 - Main brand color */
.bg-primary-700   /* #047857 */
.bg-primary-900   /* #064E3B - Darkest */

/* CSS Variables */
var(--color-primary-600)
var(--color-primary-500)
```

### Secondary Colors (Amber/Yellow)
```css
/* CSS Classes */
.bg-secondary-50   /* #FFFBEB */
.bg-secondary-100  /* #FEF3C7 */
.bg-secondary-500  /* #F59E0B - Main accent color */
.bg-secondary-600  /* #D97706 */

/* CSS Variables */
var(--color-secondary-500)
var(--color-secondary-600)
```

### Neutral Colors
```css
/* CSS Classes */
.bg-slate-50      /* #F8FAFC */
.bg-slate-100     /* #F1F5F9 */
.bg-slate-600     /* #475569 */
.bg-slate-900     /* #0F172A */

.text-slate-600   /* Body text */
.text-slate-700   /* Labels */
.text-slate-900   /* Headings */
```

## Typography

### Heading Classes
```css
.text-hero        /* 6rem, extrabold, Anton font */
.text-display     /* 5rem, bold, Poppins font */
.text-heading-1   /* 3rem, bold, Poppins font */
.text-heading-2   /* 2.25rem, bold, Poppins font */
.text-heading-3   /* 1.5rem, semibold, Poppins font */
```

### Body Text Classes
```css
.text-body-large  /* 1.25rem, Roboto font */
.text-body        /* 1rem, Roboto font */
.text-body-small  /* 0.875rem, Roboto font */
```

### Gradient Text
```css
.text-gradient-primary    /* Primary green gradient */
.text-gradient-secondary  /* Secondary amber gradient */
```

## Component Classes

### Buttons
```css
/* Primary Button - Amber background */
.btn-primary {
  @apply bg-secondary-500 text-neutral-black font-bold px-6 py-3 rounded-full;
  @apply hover:bg-secondary-600 hover:scale-[1.02] hover:shadow-large;
  @apply focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-opacity-20;
}

/* Secondary Button - Transparent with border */
.btn-secondary {
  @apply bg-transparent text-white border-2 border-white font-semibold px-5 py-2.5 rounded-full;
  @apply hover:bg-white hover:text-primary-600 hover:scale-[1.02];
  @apply focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-20;
}

/* Glassmorphism Button */
.btn-glassmorphism {
  @apply bg-white/20 backdrop-blur-xl text-white border border-white/30 px-6 py-3 rounded-xl;
  @apply hover:bg-white/30;
  @apply focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-20;
}
```

### Cards
```css
/* Standard Card */
.card {
  @apply bg-white rounded-xl shadow-card border border-slate-200 p-8;
  @apply hover:shadow-large hover:-translate-y-0.5;
  @apply transition-all duration-300 ease-out;
}

/* Spotlight Card (for features) */
.card-spotlight {
  @apply bg-white rounded-xl shadow-card border border-slate-200 p-8;
  @apply hover:shadow-large hover:-translate-y-0.5;
  @apply transition-all duration-300 ease-out;
}

/* Glassmorphism Card */
.card-glassmorphism {
  @apply bg-white/10 backdrop-blur-2xl rounded-2xl border border-white/20 shadow-glassmorphism;
}
```

### Form Elements
```css
/* Standard Input */
.input {
  @apply w-full px-4 py-3 border border-slate-300 rounded-lg bg-white text-base;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-opacity-10 focus:border-primary-600;
  @apply transition-all duration-300 ease-out;
}

/* Error State */
.input-error {
  @apply border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-opacity-10 focus:border-red-500;
}

/* Glassmorphism Input (for hero sections) */
.input-glassmorphism {
  @apply bg-transparent border-none text-white placeholder-white/80 px-4 py-3 text-base;
  @apply focus:outline-none focus:ring-0;
}

/* Label */
.label {
  @apply text-sm font-medium text-slate-700 mb-2 block;
}
```

## Effects and Utilities

### Glassmorphism
```css
.glassmorphism {
  background: var(--gradient-glassmorphism);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glassmorphism-dark {
  background: var(--gradient-glassmorphism-dark);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}
```

### Shadows
```css
.shadow-subtle        /* 0 2px 4px rgba(0, 0, 0, 0.05) */
.shadow-medium        /* 0 4px 8px rgba(0, 0, 0, 0.1) */
.shadow-large         /* 0 10px 20px rgba(0, 0, 0, 0.15) */
.shadow-card          /* 0 8px 16px rgba(0,0,0,0.1) */
.shadow-glassmorphism /* 0 8px 32px rgba(0, 0, 0, 0.1) */
.shadow-glow          /* 0 0 20px rgba(5, 150, 105, 0.3) */
```

### Animations
```css
.animate-fade-in      /* Fade in with slight upward movement */

/* Hover effects */
hover:scale-[1.02]    /* Subtle scale on hover */
hover:-translate-y-0.5 /* Subtle lift on hover */
```

## Usage Examples

### Hero Section
```astro
<section class="relative min-h-screen overflow-hidden">
  <div class="absolute inset-0 z-10" style="background: var(--gradient-hero);"></div>
  <div class="relative z-20 flex items-center justify-center min-h-screen">
    <div class="text-center">
      <h1 class="text-hero text-white mb-6">
        Find Your Perfect
        <span class="text-secondary-400 block mt-2">Venue</span>
      </h1>
      <div class="glassmorphism rounded-2xl p-4">
        <input class="input-glassmorphism" placeholder="Where?" />
        <button class="btn-primary">Search</button>
      </div>
    </div>
  </div>
</section>
```

### Feature Cards
```astro
<section class="py-24">
  <div class="max-w-7xl mx-auto px-4">
    <h2 class="text-display text-center mb-20">
      <span class="text-gradient-primary">Features</span>
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="card-spotlight">
        <h3 class="text-heading-3 mb-4">Feature Title</h3>
        <p class="text-body">Feature description...</p>
      </div>
    </div>
  </div>
</section>
```

### Forms
```astro
<form class="space-y-6">
  <div>
    <label class="label">Email Address</label>
    <input type="email" class="input" placeholder="Enter your email" />
  </div>
  <button type="submit" class="btn-primary w-full">
    Submit
  </button>
</form>
```

### Navigation Dock
```tsx
<div className="glassmorphism rounded-2xl p-4">
  <div className="flex items-center gap-2">
    {items.map((item) => (
      <button className="w-16 h-16 rounded-xl text-slate-600 hover:bg-slate-100 hover:text-slate-900 hover:scale-110 hover:-translate-y-0.5 transition-all duration-300">
        {item.icon}
      </button>
    ))}
  </div>
</div>
```

## Best Practices

1. **Consistency**: Always use design system classes instead of custom styles
2. **Hierarchy**: Use proper heading levels (text-hero > text-display > text-heading-1, etc.)
3. **Spacing**: Use the 8px spacing scale (spacing-sm, spacing-md, etc.)
4. **Colors**: Stick to the defined color palette
5. **Animations**: Use consistent transition durations (300ms) and easing (ease-out)
6. **Glassmorphism**: Use for overlay elements and hero sections
7. **Shadows**: Apply appropriate shadow levels based on element importance

## CSS Variables Reference

All design tokens are available as CSS variables in `src/layouts/BaseLayout.astro`:

```css
/* Colors */
--color-primary-600: #059669;
--color-secondary-500: #F59E0B;
--color-slate-900: #0F172A;

/* Typography */
--font-primary: 'Poppins', 'Helvetica Neue', Arial, sans-serif;
--font-body: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
--font-display: 'Anton', sans-serif;

/* Spacing */
--spacing-md: 1rem;
--spacing-lg: 1.5rem;
--spacing-xl: 2rem;

/* Shadows */
--shadow-card: 0 8px 16px rgba(0,0,0,0.1);
--shadow-glassmorphism: 0 8px 32px rgba(0, 0, 0, 0.1);

/* Gradients */
--gradient-primary: linear-gradient(135deg, #059669 0%, #10B981 100%);
--gradient-hero: linear-gradient(135deg, rgba(5, 150, 105, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
```
