I'm ready to begin implementing features from the Trodoo venue booking application based on the architecture documentation in `docs/02-new-expanded-arch.md` and the UI/UX specifications in `docs/03-features-design.md`. and adhering strictly to the design system and components already established in `docs/design.json`

For each feature I request, please:

1. **First, analyze the requirements** by reviewing the relevant documentation files to understand:
   - The feature's technical requirements and architecture
   - UI/UX design specifications and user flows (make adjustments where necessay)
   - Integration points with existing systems (PocketBase, payment services, etc.)
   - Dependencies on other features or components
   - Again, adhere strictly to the design system and components already established in `docs/design.json`

2. **Create a comprehensive task breakdown** that includes:
   - Granular, achievable tasks (each representing ~20 minutes of development work)
   - Clear task hierarchy with subtasks where appropriate
   - Dependencies between tasks clearly identified
   - Specific deliverables for each task (components, API endpoints, database schemas, etc.)
   - Always complete the all the tasks generated for the goal.

3. **Follow the established project structure** from our previous work:
   - Use the Trodoo design system (design.json as single source of truth)
   - Maintain the organized file structure (auth, common, core, dashboard, admin, venues, transactions)
   - Integrate with existing PocketBase collections and authentication (use the pocketbase-server mcp to interact my pocketbase instatnce)
   - Follow Astro v5.10 + TypeScript + Tailwind patterns

4. **Use task management tools** to track progress and ensure nothing is missed

5. **Use context7 mcp server** for updated documentation on libraries for up to date implementation

I will provide you with Feature 6 details next, and you should create a detailed implementation plan with trackable tasks before beginning any code changes.


Feature 6: System Services (Expanded)
Feature Goal: To create a reliable, decoupled backend service that handles all asynchronous tasks, including search syncing, payment processing, and the full suite of lifecycle notifications.

API Relationships: Deno <-> Pocketbase, Deno <-> Meilisearch, Paystack <-> Deno, Deno <-> Email Provider (e.g., Resend).

Detailed Feature Requirements:

Sync Service: Any C/U/D operation on a published venue in Pocketbase must be reflected in the Meilisearch index within seconds.

Webhook Service: Must reliably receive and process payment confirmation webhooks from Paystack. Must be resilient to duplicate events.

Payout Service: Must run on a regular schedule to process pending payouts for completed bookings.

Notification Service: Must handle a wide range of scheduled and transactional emails: Welcome, Booking Alerts, Check-in/Checkout Reminders, Review Prompts, and user-triggered Invitations.

Detailed Implementation Guide:
(The implementation for Sync, Webhook, and Payout services remains the same. The following details the expanded Notification Service logic within the same Deno application.)

System Architecture: Within the existing Deno application, the notificationService.ts becomes a central hub. It will be triggered by other services and multiple cron jobs.

Implementation - Notification Service:

Transactional Triggers:

On New User: A Pocketbase subscription pb.collection('users').subscribe('create', ...) will trigger the service to send a Welcome Email. Pocketbase itself sends the verification email upon registration.

On Booking Status Change: Subscriptions or webhooks on the bookings collection trigger confirmation, denial, and payment success emails.

On Invitation Request: An API call from the frontend triggers the sendInvitationEmail function.

Scheduled Triggers (Cron Jobs):

Upcoming Booking Reminder: Runs hourly. Queries for bookings starting in the next 24-25 hours and sends a reminder.

Check-in Reminder: Runs every 15 mins. Queries for bookings starting in the next 60-75 mins and sends a "Check-in soon" email with key details.

Checkout Reminder: Runs hourly. Queries for bookings ending in the next 2-3 hours and sends a reminder, perhaps including a link to the move-out checklist.

Review Prompt: Runs daily. Queries for bookings that completed the previous day and sends a "Leave a review" prompt.

Database Schema Design:

notifications Collection:

id, booking: (Relation), user: (Relation, Required)

type: (Select: welcome, booking_confirmed, checkin_reminder, review_prompt, etc.)

sent_at: (Date, Required)

This collection is crucial for preventing duplicate notifications from being sent by the cron jobs.

Error Handling & Logging: All external API calls (especially to the email provider) must be wrapped in try...catch. A failed critical email (like a booking confirmation) should trigger an alert for manual review. A robust retry mechanism is essential.


Here is the design

Feature 6: System Services (User Experience Manifestations)
Screen 1: Real-Time Search Experience (Result of Meilisearch Sync)
Screen 1 State 1: Instant Feedback
UI & UX: This isn't a new screen but an enhancement of the Venue Search Page. The design goal is to make the search feel instantaneous and intelligent. When a user types into the search bar, results stream in after a 200ms debounce. A "skeleton loader" that perfectly mimics the VenueCard layout appears for a fraction of a second, preventing content reflow and giving the illusion of an even faster load time.

Animations: The VenueCard results fade in with a subtle stagger, one after the other, creating a waterfall effect. This motion choreography makes the interface feel dynamic and responsive.

Screen 2: Real-Time Booking Status Updates (Result of Webhooks)
Screen 2 State 1: Payment Confirmation
UI & UX: The user is on their Booking Details screen after completing a payment. The UI updates without a page reload. The large status badge at the top transitions from Confirmed (styled with Secondary.yellow) to Paid (styled with a confident Primary.green). A success toast notification slides in from the top.

Toast Notification: Styled with a Primary.green background and white text/icon, it reads "Payment Successful! Your booking is confirmed." It remains on screen for 4 seconds before automatically sliding out.

Animations: The color transition of the status badge is a smooth cross-fade over 300ms, providing crucial user confidence.

Screen 2 State 2: Payout Notification
UI & UX: This state applies to the Venue Owner's Dashboard. When the Payout Service runs, the owner receives an in-app notification. A small badge appears on the "Payout Settings" navigation link. Inside this screen, a transaction history table is updated with a new line item showing the payout details and a "Paid Out" status.

Animations: The new row in the transaction history can subtly flash a light green (Primary.green at 15% opacity) for a moment upon loading to draw the owner's attention.

Screen 3: Email & In-App Alerts
Screen 3 State 1: Welcome & Verification Emails
UI & UX: The emails are designed to be clean, on-brand, and highly readable. They use the Poppins font for headings and Roboto for body text, consistent with the web app. The email template includes the company logo in the header and a simple footer. The Welcome Email is warm and inviting, highlighting key features. The Verification Email is direct and action-oriented, with a single, prominent CTA button (Components.buttons.primary style in green) that reads "Verify My Email".

Animations: While emails don't have animations, the design is mobile-first and responsive, ensuring it looks great on all clients.

Screen 3 State 2: Check-in/Checkout & Reminder Alerts
UI & UX: These alerts manifest as both emails and potentially as future in-app notifications. The emails are concise and functional. The Check-in Reminder email provides the most critical information at a glance: Venue Name, Address (as a link to Google Maps), and Booking Time. The Checkout Reminder prompts the user to complete the move-out checklist with a direct link to the checklist page in their dashboard.

Visual Hierarchy: The most important piece of information is always presented first with a larger font size (Typography.headings.h3) to support users who are on the go and scanning quickly.