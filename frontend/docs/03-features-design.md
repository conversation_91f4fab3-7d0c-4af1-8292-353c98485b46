User, Admin & Profile Core
Screen 1: Login Page
Screen 1 State 1: Default
UI & UX: The design prioritizes simplicity and trust. A two-column layout is used on desktop, gracefully stacking on mobile.

Left Column (Desktop-only): A high-quality, professional image depicting a modern, elegant event space (e.g., a corporate meeting room or a stylish loft for a party). The image is subtly overlaid with the company logo and a welcoming tagline like "Your Space, Your Success." from the brand personality. This establishes a premium, professional feel.

Right Column (Full-width on Mobile): A clean, focused form on a Neutral.lightGray background. Ample whitespace (3xl padding) creates a breathable, uncluttered feel. The headline "Welcome Back" uses Typography.headings.h2. Input fields for "Email" and "Password" follow the Components.forms.fields.input style guide. The primary CTA "Log In" uses the Components.buttons.primary style but with the Primary.green color for a brand-consistent main action. A secondary link "Don't have an account? Sign Up" is placed below in Typography.body.small with the text "Sign Up" colored in Primary.green.

Animations: On field focus, the border transitions color to Primary.green over 300ms (Animations.transitions.duration, easing). The primary button has a subtle lift (Animations.hoverEffects.buttons) on hover/press.

Screen 1 State 2: Input Validation Error
UI & UX: If the user submits an invalid email format or an incorrect password, the corresponding input field border turns to a clear error color (e.g., a standard red like #DC2626 for accessibility). An inline error message appears below the field in the same red, using Typography.body.small. For example, "Please enter a valid email address." The "Log In" button shakes horizontally for a brief moment (150ms) to signify an error, preventing user confusion.

Animations: The horizontal shake is a physics-based animation to provide clear, non-intrusive negative feedback. The error message fades in smoothly.

Screen 1 State 3: Loading
UI & UX: Upon successful form submission, the "Log In" button's text is replaced by a spinner, and the button becomes disabled. The spinner is a simple, clean line-art icon consistent with Components.icons.style.

Animations: The text fades out as the spinner fades in, preventing a jarring swap. The spinner rotates with a smooth, continuous ease-in-out animation.

Screen 2: Registration Page
Screen 2 State 1: Default
UI & UX: Follows the same layout as the Login page but with the headline "Create Your Account." It includes fields for "Full Name," "Email," and "Password." Below the password field, there is microcopy detailing the strength requirements (Typography.body.small, Neutral.darkGray). A checkbox is present for agreeing to Terms of Service, which is a required action before the "Sign Up" button becomes active.

Animations: The "Sign Up" button remains in a disabled state (e.g., Neutral.mediumGray background) until all required fields are valid and the ToS checkbox is ticked, providing clear affordance.

Screen 3: User Dashboard - Profile
Screen 3 State 1: View Mode
UI & UX: A clean, organized page with a two-column layout on desktop.

Left Column: A navigation menu for dashboard sections: "My Bookings," "My Venues" (if owner), "Profile," "Payout Settings" (if owner). The active link uses Components.navigation.activeState.

Right Column: A "Profile Information" card (Components.cards.default). It displays the user's avatar, name, and email as static text. An "Edit Profile" button (Components.buttons.secondary but with Primary.green border/text) is present. Below this card, a "My Roles" section allows users to see their current roles. If they are only a 'renter', a CTA "Become a Venue Owner" is displayed to guide them towards listing a property.

Animations: Smooth transitions between dashboard sections without a full page reload, with the content of the right column fading in/out.

Screen 3 State 2: Edit Mode
UI & UX: Clicking "Edit Profile" transforms the static text fields into input fields (Components.forms.fields.input) populated with the user's current information. The user's avatar becomes a clickable element to upload a new image. Two buttons appear at the bottom: "Save Changes" (Components.buttons.primary in green) and "Cancel" (Components.buttons.secondary in gray).

Animations: The transition to edit mode is instant. The "Save Changes" button has a loading state identical to the login button to provide feedback during the API call.

Screen 4: Admin Dashboard
Screen 4 State 1: Default View (Flagged Content)
UI & UX: This is a protected route, inaccessible to non-admins. The layout is utilitarian and data-dense. A primary navigation allows switching between "Flagged Content" and "User Management." The main view is a data table.

Table: Columns for Content Type, Content ID (links to the venue/profile), Reporter, Reason, and Status. Each row has an action menu (... icon) with options to "View Content" and "Resolve Flag." The table header is sticky.

Animations: Table rows have a subtle Neutral.lightGray background on hover to improve tracking.

Screen 4 State 2: User Management View
UI & UX: Another data table view with columns for User Avatar, Name, Email, Roles, and Status (Active/Inactive). A search bar above the table allows filtering by name or email. Each row has an action menu to "Edit User" or "Deactivate User."

Animations: The search provides live filtering of the table content with a smooth re-render of the rows.

Feature 2: Venue Marketplace (Listing, Touring & Search)
Screen 1: Venue Search Page
Screen 1 State 1: Default / Initial Load
UI & UX: The centerpiece is a large, inviting search bar. The bar itself is styled with a Shadows.medium to make it prominent. Inside the bar is a search icon (Components.icons.style in Primary.green) and placeholder text "Search for a venue by city, name, or type...". Below the search bar, a grid of "Venue Cards" displays featured or recently added venues to prevent an empty state and encourage discovery.

Animations: The search bar has a subtle glow effect on focus. As the user starts typing, the rest of the page content (featured grid) fades out to give full attention to the search results.

Screen 1 State 2: Active Search & Results
UI & UX: As the user types, a list of search results instantly populates below the search bar. Each result is a VenueCard component, which includes a high-quality thumbnail image, the venue title (Typography.headings.h3), address (Typography.body.small), and price_per_hour. The layout is a responsive grid.

Animations: Results fade in with a slight stagger (Animations.transitions), making the experience feel fluid and fast. A skeleton loader matching the VenueCard layout is shown for a split second while Meilisearch returns results to manage perceived performance.

Screen 2: Single Venue Page
Screen 2 State 1: Default View
UI & UX: A content-first layout.

Header: The PannellumViewer component takes up the top section of the page (e.g., 40vh), providing an immersive, interactive 360° view.

Body: Below the viewer, the layout splits into two columns on desktop.

Left Column (Main Content): Venue title (Typography.headings.h2), address, followed by a detailed description. A section for Amenities uses tag-like components for easy scanning. A photo gallery shows the standard_photos.

Right Column (Action Panel): A sticky Components.cards.bookingForm contains the booking request widget. This card stays visible as the user scrolls the main content. It includes date/time pickers and a dynamically updated price. The primary CTA is "Request to Book."

Flagging: A small, unobtrusive "Flag" icon with text is present near the bottom of the main content column.

Animations: Scrolling down the page causes the 360° viewer to shrink or parallax scroll to save vertical space. The booking card's stickiness is smooth.

Screen 2 State 2: Flag Modal
UI & UX: Clicking the "Flag" icon opens a modal window. The modal has a clear title "Report this Venue." It contains a text area for the user to input their Reason and a primary "Submit Report" button.

Animations: The modal fades in over the page with a background overlay, focusing user attention. Upon successful submission, a success toast/notification appears briefly before the modal closes.

Screen 3: Create/Edit Venue Form
Screen 3 State 1: Multi-Step Wizard
UI & UX: A multi-step form to prevent overwhelming the user.

Step 1: The Basics: title, description, address.

Step 2: Details & Amenities: capacity, price_per_hour, amenities (multi-select checklist).

Step 3: Media: File uploaders for standard_photos, pano_photo, and rental_agreement_pdf. The uploaders show file previews and progress bars.

A progress bar at the top of the page shows the user's location in the flow (e.g., Step 1 of 3). "Next" and "Back" buttons provide clear navigation. The final button is "Publish Venue."

Animations: Transitions between steps are a smooth horizontal slide. Progress bar animates as the user completes steps.

Feature 3: Transaction Lifecycle (Booking, Messaging, Payments & Payouts)
Screen 1: Venue Page - Booking Widget
Screen 1 State 1: Date Selection
UI & UX: The Components.cards.bookingForm on the venue page. It contains two Components.forms.fields.dateInput fields for "Start Date" and "End Date." A calendar pops up for selection. The calendar view disables dates that are already booked (!db.exists(...) logic). As dates are selected, a price breakdown appears below (price_per_hour x hours = total_price).

Animations: The calendar component animates smoothly into view. The price updates instantly with a quick fade-in animation on the numbers.

Screen 2: User Dashboard - Bookings
Screen 2 State 1: List of Bookings
UI & UX: A tabbed interface to separate "Upcoming," "Past," and (for owners) "Pending Requests." Each booking is represented by a card containing the venue image/title, dates, and a clear status badge (e.g., Pending, Confirmed, Paid).

Animations: Status badges use color to communicate state: Secondary.yellow for pending, Primary.green for confirmed/paid, Neutral.darkGray for past.

Screen 2 State 2: Booking Details & Chat
UI & UX: Clicking a booking card navigates to a detail page.

Owner View (Pending Request): Shows booking details and two prominent buttons: "Approve" (Primary.green) and "Deny" (a less prominent red).

Renter View (Confirmed): Shows a "Pay Now" button (Secondary.yellow). Clicking this initiates the Paystack payment flow.

Paid/Active View (Both Users): The main feature is the MessagingWindow component. It's a simple chat interface with messages aligned left/right based on the sender. Timestamps are visible. The message input field is at the bottom.

Animations: New messages arrive in real-time and scroll smoothly into view. The status of the booking at the top of the page updates in real-time (e.g., from Confirmed to Paid) with a subtle color-fade transition.

Feature 4: Community Trust & Feedback (Reviews & Ratings)
Screen 1: Leave a Review
Screen 1 State 1: Prompt
UI & UX: After a booking is completed, a dismissible banner appears in the user's main dashboard prompting them to "Leave a review for [Venue Name]". This banner is styled with a Primary.green background and white text to be inviting.

Animations: The banner slides down from the top of the viewport.

Screen 1 State 2: Review Form Modal
UI & UX: Clicking the prompt opens a modal (Components.cards.default styling). The modal is titled "How was your experience at [Venue Name]?". It contains the interactive StarRating.jsx component for the "Overall Experience" rating. Below is a textarea for the comment. The primary CTA is "Submit Review".

Animations: The stars in the rating component fill with the Secondary.yellow color on hover and click, with a subtle "pop" animation to make the interaction feel satisfying.

Screen 2: Venue Page - Reviews Section
Screen 2 State 1: Default
UI & UX: On the single venue page, below the main content, is a dedicated "Reviews" section. At the top, it displays the aggregate average_rating next to a series of filled stars, and the total number of reviews (e.g., "4.8 stars - 23 reviews"). Below is a paginated list of individual reviews.

Review Card: Each review shows the renter's name and avatar, the star rating they gave, their comment, and the date. If an owner_response exists, it is displayed nested underneath, slightly indented and with a "Response from the owner:" label, to create a clear conversational hierarchy.

Animations: Individual reviews fade in as the user scrolls down (lazy loading).

Feature 5: Lifecycle Communications & Logistics (Revised)
Screen 1: Owner Dashboard - Checklist Creation
Screen 1 State 1: Form
UI & UX: In the "Edit Venue" flow, a new tab/section called "Checklist" allows owners to manage the move-out checklist. The UI consists of a list of text input fields. Each field represents one checklist item. Owners can add more items or delete existing ones dynamically. The interface is clean and tool-like, prioritizing function over form.

Animations: When an owner clicks "Add Item," a new input field smoothly animates into the list, preventing a jarring layout shift.

Screen 2: Booking Details - Renter's Checklist
Screen 2 State 1: To-Do
UI & UX: On the booking detail page, once a booking is in its final day, a "Move-out Checklist" section appears. It displays the owner-defined items, each with a custom-styled checkbox. The section has a clear header and a "Submit to Owner" button that is disabled until all items are checked.

Animations: As the renter checks each item, the text gets a strikethrough, and the checkbox fills with Primary.green, providing clear, satisfying feedback of completion.

Screen 3: Booking Details - Renter's Invitations
Screen 3 State 1: Invitation Form
UI & UX: After a booking is Paid, a new "Invite Guests" button appears on the booking detail page. Clicking it opens a modal. The modal has a field for a custom message and a larger text area to paste a list of guest emails, one per line. The primary CTA is "Send Invitations". The design makes it clear that the platform is sending emails on the user's behalf.

Animations: On successful submission, the button enters a loading state, then transitions to a success state. A confirmation toast appears: "[Number] invitations have been sent."

Screen 4: In-App User Onboarding & Verification
Screen 4 State 1: Unverified User Banner
UI & UX: A persistent, non-intrusive banner is displayed at the top of every page for users whose verified status is false. The banner has a Secondary.yellow background to draw attention without being an alarming error state. It contains the text "Please check your email to verify your account." and a "Resend Email" button.

Animations: The banner slides down smoothly on initial page load. When "Resend Email" is clicked, it shows a temporary "Sent!" confirmation before reverting, providing feedback without a full page reload.

Feature 6: System Services (User Experience Manifestations)
Screen 1: Real-Time Search Experience (Result of Meilisearch Sync)
Screen 1 State 1: Instant Feedback
UI & UX: This isn't a new screen but an enhancement of the Venue Search Page. The design goal is to make the search feel instantaneous and intelligent. When a user types into the search bar, results stream in after a 200ms debounce. A "skeleton loader" that perfectly mimics the VenueCard layout appears for a fraction of a second, preventing content reflow and giving the illusion of an even faster load time.

Animations: The VenueCard results fade in with a subtle stagger, one after the other, creating a waterfall effect. This motion choreography makes the interface feel dynamic and responsive.

Screen 2: Real-Time Booking Status Updates (Result of Webhooks)
Screen 2 State 1: Payment Confirmation
UI & UX: The user is on their Booking Details screen after completing a payment. The UI updates without a page reload. The large status badge at the top transitions from Confirmed (styled with Secondary.yellow) to Paid (styled with a confident Primary.green). A success toast notification slides in from the top.

Toast Notification: Styled with a Primary.green background and white text/icon, it reads "Payment Successful! Your booking is confirmed." It remains on screen for 4 seconds before automatically sliding out.

Animations: The color transition of the status badge is a smooth cross-fade over 300ms, providing crucial user confidence.

Screen 2 State 2: Payout Notification
UI & UX: This state applies to the Venue Owner's Dashboard. When the Payout Service runs, the owner receives an in-app notification. A small badge appears on the "Payout Settings" navigation link. Inside this screen, a transaction history table is updated with a new line item showing the payout details and a "Paid Out" status.

Animations: The new row in the transaction history can subtly flash a light green (Primary.green at 15% opacity) for a moment upon loading to draw the owner's attention.

Screen 3: Email & In-App Alerts
Screen 3 State 1: Welcome & Verification Emails
UI & UX: The emails are designed to be clean, on-brand, and highly readable. They use the Poppins font for headings and Roboto for body text, consistent with the web app. The email template includes the company logo in the header and a simple footer. The Welcome Email is warm and inviting, highlighting key features. The Verification Email is direct and action-oriented, with a single, prominent CTA button (Components.buttons.primary style in green) that reads "Verify My Email".

Animations: While emails don't have animations, the design is mobile-first and responsive, ensuring it looks great on all clients.

Screen 3 State 2: Check-in/Checkout & Reminder Alerts
UI & UX: These alerts manifest as both emails and potentially as future in-app notifications. The emails are concise and functional. The Check-in Reminder email provides the most critical information at a glance: Venue Name, Address (as a link to Google Maps), and Booking Time. The Checkout Reminder prompts the user to complete the move-out checklist with a direct link to the checklist page in their dashboard.

Visual Hierarchy: The most important piece of information is always presented first with a larger font size (Typography.headings.h3) to support users who are on the go and scanning quickly.