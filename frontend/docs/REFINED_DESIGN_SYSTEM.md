# Refined Design System - Trodoo

## Overview
This document outlines the refined, more professional design system for the Trodoo venue rental platform. The colors have been toned down to create a more sophisticated and accessible user experience while maintaining the green/yellow brand identity.

## Color Palette

### Primary Colors (Refined Green)
- **Primary Green**: `#059669` (Emerald-600) - Main brand color, more sophisticated than bright green
- **Primary Light**: `#10B981` (Emerald-500) - Lighter accent, professional yet vibrant
- **Primary Dark**: `#047857` (Emerald-700) - Deeper shade for contrast and hierarchy
- **Primary Subtle**: `#D1FAE5` (Emerald-100) - Very light background, gentle on the eyes
- **Primary Muted**: `#6EE7B7` (Emerald-300) - Muted accent for secondary elements

### Secondary Colors (Refined Yellow/Amber)
- **Secondary Yellow**: `#F59E0B` (Amber-500) - Refined yellow, less harsh than bright yellow
- **Secondary Light**: `#FBBF24` (Amber-400) - Softer highlight color
- **Secondary Dark**: `#D97706` (Amber-600) - Professional dark amber
- **Secondary Subtle**: `#FEF3C7` (Amber-100) - Very light background
- **Secondary Muted**: `#FCD34D` (Amber-300) - Muted accent

### Neutral Colors (Enhanced)
- **White**: `#FFFFFF` - Pure white for backgrounds
- **Light Gray**: `#F9FAFB` (Gray-50) - Softer than stark white
- **Medium Gray**: `#E5E7EB` (Gray-200) - Refined border and separator color
- **Dark Gray**: `#6B7280` (Gray-500) - Better contrast for text
- **Charcoal**: `#374151` (Gray-700) - Professional dark text
- **Black**: `#1F2937` (Gray-800) - Softer than pure black, easier on eyes

### Slate Palette (Additional Options)
Complete slate color range from 50-900 for more nuanced design choices.

## Typography

### Font Families
- **Primary (Headings)**: `'Poppins', 'Helvetica Neue', Arial, sans-serif`
- **Body Text**: `'Roboto', 'Helvetica Neue', Arial, sans-serif`

### Font Weights
- **Regular**: 400 - Body text
- **Medium**: 500 - Labels and secondary text
- **Semi-Bold**: 600 - Subheadings
- **Bold**: 700 - Headings and CTAs

## Spacing System (8px Base Unit)

- **xs**: 4px
- **sm**: 8px
- **md**: 16px
- **lg**: 24px
- **xl**: 32px
- **2xl**: 48px
- **3xl**: 64px

## Border Radius

- **sm**: 4px - Small elements
- **md**: 8px - Buttons, inputs, cards
- **lg**: 12px - Medium containers
- **xl**: 20px - Large containers, dock

## Shadows (Refined)

- **Subtle**: `0 2px 4px rgba(0, 0, 0, 0.05)` - Very light shadow
- **Medium**: `0 4px 8px rgba(0, 0, 0, 0.1)` - Standard shadow
- **Large**: `0 10px 20px rgba(0, 0, 0, 0.15)` - Prominent shadow
- **Card**: `0 8px 16px rgba(0,0,0,0.1)` - Card containers

## Component Styling

### Buttons
- **Primary**: Amber background (`#F59E0B`), dark text, rounded corners
- **Secondary**: Transparent with white border, transforms to white background on hover
- **Hover Effects**: Subtle lift (1px) and shadow increase

### Cards
- **Background**: White with subtle border
- **Border Radius**: 20px (xl)
- **Shadow**: Card shadow
- **Padding**: 32px (xl)

### Dock Component
- **Items**: White background with light gray border
- **Hover**: Light green background (`#D1FAE5`) with green border
- **Icons**: Default gray (`#6B7280`), no overwhelming green
- **Container**: Semi-transparent white with subtle shadow

### Inputs
- **Border**: Light gray (`#E5E7EB`)
- **Focus**: Green border with subtle green shadow
- **Border Radius**: 8px (md)
- **Padding**: 12px 16px

## Accessibility Improvements

### Color Contrast
- All text colors meet WCAG AA standards
- Reduced saturation prevents eye strain
- Better contrast ratios for readability

### Visual Hierarchy
- Subtle color differences create clear hierarchy
- Less aggressive colors reduce cognitive load
- Professional appearance builds trust

## Implementation

### CSS Variables
All colors are available as CSS custom properties in BaseLayout.astro:
```css
:root {
  --color-primary: #059669;
  --color-secondary: #F59E0B;
  --color-neutral-black: #1F2937;
  /* ... etc */
}
```

### Tailwind Classes
Updated tailwind.config.mjs includes all refined colors:
```js
primary: {
  DEFAULT: '#059669',
  light: '#10B981',
  dark: '#047857',
  // ... etc
}
```

## Benefits of Refined System

1. **Professional Appearance**: More sophisticated color choices
2. **Better Accessibility**: Improved contrast and readability
3. **Reduced Eye Strain**: Softer colors, less harsh on users
4. **Brand Consistency**: Maintains green/yellow identity while being more refined
5. **Scalability**: More color options for complex interfaces
6. **Trust Building**: Professional colors increase user confidence

## Usage Guidelines

- Use primary green sparingly for key actions and brand elements
- Leverage neutral colors for most UI elements
- Apply secondary yellow for highlights and accents only
- Maintain consistent spacing using the 8px base unit
- Use subtle shadows to create depth without overwhelming
- Ensure sufficient contrast for all text elements

This refined design system creates a more professional, accessible, and user-friendly experience while maintaining the Trodoo brand identity.
