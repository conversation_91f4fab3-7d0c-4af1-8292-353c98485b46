## File System

### Frontend Repository (`astro-venue-frontend`)

```
/src
|-- /assets
|   |-- /fonts
|   |-- /icons
|   `-- /images
|-- /components
|   |-- /auth
|   |   |-- LoginForm.jsx
|   |   `-- RegisterForm.jsx
|   |-- /common
|   |   |-- Button.jsx
|   |   |-- Modal.jsx
|   |   `-- Spinner.jsx
|   |-- /core
|   |   |-- Header.astro
|   |   |-- Footer.astro
|   |   `-- Layout.astro
|   |-- /dashboard
|   |   |-- BookingList.jsx
|   |   |-- OwnerDashboard.jsx
|   |   |-- RenterDashboard.jsx
|   |   `-- ProfileForm.jsx
|   |-- /admin
|   |   |-- AdminDashboard.jsx
|   |   |-- FlaggedContentTable.jsx
|   |   `-- UserManagementTable.jsx
|   |-- /venues
|   |   |-- VenueCard.jsx
|   |   |-- VenueSearch.jsx
|   |   |-- VenueForm.jsx
|   |   |-- PannellumViewer.jsx
|   |   `-- BookingForm.jsx
|   `-- /transactions
|       |-- MessagingWindow.jsx
|       `-- PaystackPayment.jsx
|-- /layouts
|   `-- BaseLayout.astro
|-- /lib
|   |-- pocketbase.js      // Pocketbase client instance
|   |-- meilisearch.js     // Meilisearch client instance
|   `-- state.js           // Global state management (e.g., Nano Stores)
|-- /pages
|   |-- /auth
|   |   |-- login.astro
|   |   `-- register.astro
|   |-- /dashboard
|   |   |-- index.astro
|   |   `-- profile.astro
|   |-- /admin
|   |   `-- index.astro
|   |-- /venues
|   |   |-- index.astro      // Search page
|   |   |-- new.astro        // Create new venue page
|   |   `-- [id].astro     // Dynamic route for single venue
|   |-- /bookings
|   |   |-- index.astro      // List of user's bookings
|   |   `-- [id].astro     // Detail view of a booking with messaging
|   |-- index.astro        // Homepage
|   `-- 404.astro
`-- /styles
    `-- global.css
```

### Backend Repository (`deno-venue-services`)

```
/
|-- /src
|   |-- /services
|   |   |-- meilisearchSyncService.ts  // Logic for syncing PB -> MS
|   |   |-- paystackWebhookService.ts  // Logic for handling webhooks
|   |   `-- payoutService.ts           // Logic for processing payouts
|   |-- /types
|   |   |-- paystack.ts
|   |   `-- pocketbase.ts
|   `-- /utils
|       `-- logger.ts
|-- main.ts                      // Main server entry point (e.g., using Oak)
|-- Dockerfile
|-- deps.ts                      // Centralized dependency management
`-- .env.example
```

## Feature Specifications

### Feature 1: User, Admin & Profile Core

  * **Feature Goal:** To establish a secure and robust identity system that handles user registration, authentication, role-based access control (RBAC), and content moderation primitives. This is the bedrock of platform trust and safety.

  * **API Relationships:** This feature exclusively uses the Pocketbase API for all its operations. The frontend client will interact directly with the Pocketbase instance.

  * **Detailed Feature Requirements:**

      * Users must be able to register with an email and password.
      * Passwords must meet strength requirements (e.g., min 8 chars, 1 uppercase, 1 number).
      * Upon registration, users are assigned the 'renter' role by default. They can add the 'owner' role from their profile.
      * Users must log in to access any authenticated parts of the application.
      * A user with the 'admin' role has access to a separate dashboard.
      * The admin dashboard must display a list of all users and a list of all flagged content.
      * Admins must be able to view user details and manually change a user's role or deactivate their account.
      * Admins must be able to view flagged content details and resolve flags (marking them as 'resolved').

  * **Detailed Implementation Guide:**

    1.  **System Architecture:** A standard client-server model where the Astro/React frontend acts as the client and Pocketbase is the BaaS. The admin dashboard is a set of pages within the Astro application located at `/admin/*`, protected by a layout that verifies the user's role from the global state store.
    2.  **Database Schema Design:**
          * **`users` Collection:**
              * `id`: (PK, default)
              * `email`: (Text, Unique, Required)
              * `name`: (Text, Required)
              * `avatar`: (File)
              * `roles`: (JSON, Required, Default: `["renter"]`). Can contain 'renter', 'owner', 'admin'.
              * `is_active`: (Bool, Default: `true`)
              * `paystack_customer_id`: (Text, optional)
              * `paystack_recipient_code`: (Text, optional, for Owners)
          * **`flagged_content` Collection:**
              * `id`: (PK, default)
              * `content_type`: (Select, Options: 'venue', 'user\_profile', Required)
              * `content_id`: (Text, Required)
              * `reporter`: (Relation to `users`, Required)
              * `reason`: (Text, Required)
              * `status`: (Select, Options: 'open', 'resolved', Default: 'open', Required)
    3.  **Comprehensive API Design (Pocketbase):**
          * `POST /api/collections/users/records`: Create a user. Publicly accessible.
          * `POST /api/collections/users/auth-with-password`: Authenticates a user, returns a JWT.
          * `GET /api/collections/users/records`: List users. API Rule: `@request.auth.roles ~ 'admin'`.
          * `PATCH /api/collections/users/records/:id`: Update a user. API Rule: `@request.auth.id = id || @request.auth.roles ~ 'admin'`.
          * `POST /api/collections/flagged_content/records`: Create a flag. API Rule: `@request.auth.id != ""`.
          * `GET /api/collections/flagged_content/records`: List flags. API Rule: `@request.auth.roles ~ 'admin'`.
    4.  **Frontend Architecture:**
          * State for the authenticated user (including roles) will be managed globally using Nano Stores. On app load, a request is made to refresh the auth state from the Pocketbase cookie.
          * A `AdminLayout.astro` will wrap all pages in `/src/pages/admin/`. This layout will check the global user store; if the user is not present or does not have the 'admin' role, it will redirect to the login page or a 403 page.
    5.  **Detailed CRUD Operations:**
          * **Create:** User registration validates email format and password strength on the client before sending to Pocketbase.
          * **Read:** A user can only read their own full record. An admin can read all records with pagination.
          * **Update:** A user can update their `name` and `avatar`. An admin can update any user's `roles` and `is_active` status.
          * **Delete:** No hard deletes. Admins perform a soft delete by setting `is_active = false`. This preserves user data for historical context (e.g., past bookings).
    6.  **Security Considerations:** JWTs returned by Pocketbase will be stored in a secure, `HttpOnly` cookie. All state-changing forms will implicitly use Pocketbase's anti-CSRF protection. All user-provided text (names, etc.) will be escaped on render to prevent XSS.
    7.  **Testing Strategy:** Unit tests for React form components. Integration tests to verify API rules in Pocketbase (e.g., a non-admin user attempting to list all users should fail). E2E test for the admin login flow and resolving a flagged item.
    8.  **Data Management:** User session is managed via the JWT. Admin table data (users, flags) will use server-side pagination handled by the Pocketbase API.
    9.  **Error Handling & Logging:** Client-side form errors will be displayed next to the relevant fields. API errors (e.g., 401, 403) will be caught by a global fetch wrapper, which will redirect to the login page if necessary. Pocketbase will log all failed API rule validations.

### Feature 2: Venue Marketplace (Listing & Search)

  * **Feature Goal:** To provide a fluid and intuitive interface for owners to merchandise their venues effectively and for renters to discover them through a powerful, fast search experience.

  * **API Relationships:** The frontend interacts with Pocketbase for all CRUD operations on venues and with Meilisearch for all search queries.

  * **Detailed Feature Requirements:**

      * An authenticated owner must be able to access a multi-step "Create Venue" form.
      * The form must capture title, description, address, capacity, pricing, amenities (as selectable tags), standard photos, one equirectangular 360° photo, and an optional PDF rental agreement.
      * The listing page must feature a native 360° photo viewer.
      * The search interface must provide real-time, typo-tolerant results as the user types.
      * Any authenticated user must be able to click a "Flag" button on a venue page, which opens a modal to submit a reason for flagging.

  * **Detailed Implementation Guide:**

    1.  **System Architecture:** The frontend is the central orchestrator. For writing data (creating/updating venues), it talks to Pocketbase. For reading data via search, it talks directly to the Meilisearch instance. This decouples the search performance from the primary database.
    2.  **Database Schema Design (`venues` Collection):**
          * `id`: (PK, default)
          * `owner`: (Relation to `users`, Required, Non-empty)
          * `title`: (Text, Required)
          * `description`: (Editor, Required)
          * `address`: (JSON, e.g., `{ "street": "", "city": "", "country": "" }`)
          * `capacity`: (Number, Required)
          * `price_per_hour`: (Number, Required)
          * `amenities`: (JSON, e.g., `["wifi", "projector"]`)
          * `standard_photos`: (File, Multi-select, Max 10)
          * `pano_photo`: (File, Single-select)
          * `rental_agreement_pdf`: (File, Single-select)
          * `is_published`: (Bool, Default: `false`)
    3.  **Comprehensive API Design:**
          * **Pocketbase API:** Standard REST-like CRUD endpoints for the `venues` collection. Key API Rule on `venues`: `owner.id = @request.auth.id || @request.auth.roles ~ 'admin'`. Only the owner or an admin can create/update/delete. Public can read.
          * **Meilisearch API:**
              * `POST /indexes/venues/search`: The frontend will use this endpoint. The public, read-only search key will be exposed to the client. The request body will contain the `q` (query) parameter.
    4.  **Frontend Architecture:**
          * `VenueForm.jsx`: A multi-step wizard component using component state to manage the form data across steps before final submission.
          * `PannellumViewer.jsx`: A React component that wraps the Pannellum library. It will take the URL of the `pano_photo` from Pocketbase as a prop and initialize the viewer.
          * `VenueSearch.jsx`: This component will contain the search input. It will use a debounced effect to send search requests to the Meilisearch API as the user types, updating a list of results.
    5.  **Detailed CRUD Operations:**
          * **Create:** The `VenueForm` component collects all data. On submit, it sends a single `multipart/form-data` request to the Pocketbase `venues` collection endpoint.
          * **Read (Single Venue):** Astro's `[id].astro` page will fetch data for a single venue on the server during the build (`getStaticPaths`) or on request (SSR) for fast loads.
          * **Update:** An "Edit" button on the venue page (visible only to the owner) leads back to the `VenueForm`, pre-populated with existing data.
          * **Delete:** Owners can "unpublish" a venue (`is_published = false`), which is a soft delete. Admins can perform a hard delete.
    6.  **Security Considerations:** The Meilisearch instance must be configured with a public key that only allows search operations and a private admin key stored securely in the Deno service for writing/updating documents. All uploaded files (photos, PDFs) must be scanned for malware on the server-side if possible, or limited by type and size.
    7.  **Testing Strategy:** Unit tests for the `PannellumViewer` props and the `VenueSearch` debouncing logic. Integration test to ensure creating a venue in Pocketbase correctly triggers the sync to Meilisearch. E2E test of the full create-listing-then-search-for-it flow.
    8.  **Data Management:** Astro's static generation for individual venue pages provides excellent performance. Search results from Meilisearch will be paginated. Images should be served through a CDN with image optimization if possible.
    9.  **Error Handling & Logging:** If the Meilisearch service is down, the search bar should display a graceful error message. If a 360° photo fails to load, the `PannellumViewer` component should display a placeholder. Failed file uploads during venue creation should show a specific error to the user.

### Feature 3: Transaction Lifecycle (Booking, Payments & Payouts)

  * **Feature Goal:** To manage the entire commercial flow in a secure, reliable, and auditable manner, from a renter's initial request through to the owner receiving their funds.

  * **API Relationships:** Frontend \<-\> Pocketbase (Booking & Chat), Frontend -\> Paystack (Payment), Deno Service \<-\> Paystack (Payouts & Webhooks).

  * **Detailed Feature Requirements:**

      * On a venue page, an authenticated renter can select a start and end date/time and request to book.
      * This request must trigger a check against existing confirmed bookings for that venue to prevent double-booking.
      * The property owner receives a notification and can approve or deny the request from their dashboard.
      * Upon approval, the renter receives a notification and a prompt to pay.
      * The renter pays the full amount via Paystack.
      * Upon successful payment, the booking status is updated, and both parties are notified.
      * After the booking's end date has passed, a payout is automatically scheduled for the owner.
      * A simple, real-time chat must be available within the context of a single booking, accessible only to the renter and owner.

  * **Detailed Implementation Guide:**

    1.  **System Architecture:** This is a multi-step, event-driven flow.
        1.  `Client -> PB`: Renter submits booking request.
        2.  `PB -> Client`: Owner sees pending request on their dashboard.
        3.  `Client -> PB`: Owner approves request, status changes to `confirmed`.
        4.  `PB -> Client`: Renter sees "Pay Now" button.
        5.  `Client -> Paystack`: Renter completes payment.
        6.  `Paystack -> Deno Webhook`: Paystack sends `charge.success` event.
        7.  `Deno -> PB`: Deno service updates booking status to `paid`.
        8.  `Deno (Scheduler) -> PB`: Deno service finds `completed` bookings.
        9.  `Deno -> Paystack`: Deno service initiates transfer to owner.
    2.  **Database Schema Design:**
          * **`bookings` Collection:**
              * `id`, `renter`, `owner`, `venue`: (Relations, Required)
              * `start_date`: (Date, Required)
              * `end_date`: (Date, Required)
              * `total_price`: (Number, Required)
              * `platform_fee`: (Number, Required)
              * `payout_amount`: (Number, Required)
              * `status`: (Select: `pending`, `confirmed`, `paid`, `completed`, `cancelled`, `denied`, Required)
              * `paystack_ref`: (Text)
          * **`messages` Collection:**
              * `id`, `booking`: (Relation, Required), `sender`: (Relation, Required), `content`: (Text, Required)
          * **API Rules on `bookings` (CRITICAL):**
              * **Create Rule:** `@request.auth.id != "" && @request.data.start_date < @request.data.end_date && !db.exists("bookings", "venue = @request.data.venue && id != @request.id && (start_date < @request.data.end_date && end_date > @request.data.start_date) && status = 'paid'")`
              * **View Rule:** `renter.id = @request.auth.id || owner.id = @request.auth.id || @request.auth.roles ~ 'admin'`
    3.  **Comprehensive API Design:**
          * The Deno service will expose one webhook endpoint: `POST /api/webhooks/paystack`. It must validate the signature and be idempotent.
          * The Deno service's payout logic will be triggered internally by a cron job (e.g., using Deno's `cron` library or a host-level cron).
    4.  **Frontend Architecture:** The `BookingForm.jsx` will handle date selection and price calculation. The `MessagingWindow.jsx` will subscribe to the `messages` collection for a specific booking ID using the Pocketbase JS SDK's realtime `subscribe` method.
    5.  **Detailed CRUD Operations:**
          * A booking is **Created** with `pending` status.
          * It's **Updated** by the owner to `confirmed` or `denied`.
          * It's **Updated** by the system to `paid` via webhook.
          * It's **Updated** by a daily cron job to `completed` if `end_date` is in the past.
    6.  **Security Considerations:** The Deno webhook endpoint **MUST** verify the `x-paystack-signature` header on every request to prevent spoofing. Environment variables for Paystack secrets and Pocketbase admin credentials must be stored securely and not exposed to the client. The logic for calculating `platform_fee` and `payout_amount` must be on the secure backend (Deno service), not the client.
    7.  **Testing Strategy:** Critical integration test for the double-booking prevention API rule in Pocketbase. Integration test for the Deno webhook handler using mock Paystack event payloads. Test the payout logic with various fee calculations.
    8.  **Data Management:** Chat messages are real-time. Booking statuses should be re-fetched on key events or via polling/subscription to give users up-to-date information.
    9.  **Error Handling & Logging:** If a payment webhook is received for a non-existent booking reference, log it as a critical error. If a payout transfer fails, the system should retry a limited number of times and then flag the payout for manual admin intervention.

### Feature 4: System Services (Sync & Webhooks)

  * **Feature Goal:** To create a reliable, decoupled backend service that handles asynchronous tasks critical to the platform's operation, namely keeping the search index current and processing financial transactions.

  * **API Relationships:** This service acts as the glue code. Deno -\> Pocketbase (Admin access), Deno -\> Meilisearch (Admin access), Paystack -\> Deno (Webhook).

  * **Detailed Feature Requirements:**

      * **Sync Service:** Any create, update, or delete operation on a published venue in Pocketbase must be reflected in the Meilisearch index within seconds.
      * **Webhook Service:** Must reliably receive and process payment confirmation webhooks from Paystack. Must be resilient to duplicate events.
      * **Payout Service:** Must run on a regular schedule (e.g., daily) to process pending payouts for completed bookings.

  * **Detailed Implementation Guide:**

    1.  **System Architecture:** A single Deno application deployed in a Docker container on Fly.io/Railway. The application will run a web server (for the webhook) and a background process (for the sync and scheduled payouts) concurrently.
    2.  **Implementation - Sync Service:**
          * On startup, the Deno service authenticates with Pocketbase as an admin.
          * It subscribes to the `venues` collection: `pb.collection('venues').subscribe('*', ...)`
          * Inside the callback, it receives an `event` object (`event.action`, `event.record`).
          * `if (event.action === 'create' || event.action === 'update')`: format the `event.record` into the structure Meilisearch expects and call `meiliClient.index('venues').addDocuments([record])`.
          * `if (event.action === 'delete')`: call `meiliClient.index('venues').deleteDocument(event.record.id)`.
          * Include a startup script to perform a full one-time sync of all existing venues to handle initial deployment and disaster recovery.
    3.  **Implementation - Webhook Service:**
          * Use a Deno web framework like Oak. Define the `POST /api/webhooks/paystack` route.
          * **Step 1: Verify Signature.** Create a crypto HMAC-SHA512 hash of the request body using your Paystack secret key. Compare it to the `x-paystack-signature` header. If they don't match, return `400 Bad Request` immediately.
          * **Step 2: Check Idempotency.** When a `charge.success` event comes in, check in the `bookings` collection if a booking with the same `paystack_ref` already has the status `paid`. If so, return `200 OK` to acknowledge the webhook but do nothing further.
          * **Step 3: Process.** If the signature is valid and the event is new, find the booking, update its status to `paid`, and save the record.
    4.  **Implementation - Payout Service:**
          * Use a library like `deno-cron` to schedule a function `processPayouts` to run once daily.
          * The function will:
              * Get the current date. Find all `bookings` where `status = 'paid'` and `end_date` is in the past. Update their status to `completed`.
              * Find all `bookings` where `status = 'completed'` and for which no `payout` record exists.
              * For each of these, calculate the `payout_amount` (`total_price` - `platform_fee`).
              * Call the Paystack Transfers API to send the `payout_amount` to the owner's `paystack_recipient_code`.
              * Create a `payouts` record to log the transaction.
    5.  **Security:** This service is the most sensitive part of the architecture. All secret keys (Pocketbase admin, Meilisearch admin, Paystack secret) **MUST** be injected as environment variables into the container and should never be hardcoded. Network policies on the hosting provider should limit ingress traffic to only what's necessary (e.g., port 443 from Paystack's IP range for the webhook).
    6.  **Testing Strategy:** Pure unit tests for the data transformation logic (PB record -\> MS document). Integration tests for each service function using mocked SDK clients (mock `pb`, `meili`, `paystack`).
    7.  **Data Management:** This service is stateless. Its primary job is to read state from one service and update state in another.
    8.  **Error Handling & Logging:** Wrap all external API calls in `try...catch` blocks. If the Meilisearch sync fails, log the failed record ID and retry with exponential backoff. If a payout fails, update the associated `payouts` record with an 'error' status and reason, and send an alert to an admin channel (e.g., a Slack webhook). Use structured JSON logging for all operations.