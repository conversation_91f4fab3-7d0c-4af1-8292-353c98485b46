# Reviews & Ratings Feature Implementation

This document outlines the implementation of Feature 4: Community Trust & Feedback (Reviews & Ratings) for the Trodoo venue booking platform.

## Overview

The reviews system allows renters to leave ratings and comments for venues after completing their bookings, and enables venue owners to respond to reviews. This builds trust and transparency in the marketplace.

## Database Schema

### Reviews Collection

The `reviews` collection has been designed with the following schema:

```javascript
{
  name: 'reviews',
  type: 'base',
  schema: [
    {
      name: 'booking',
      type: 'relation',
      required: true,
      options: { collectionId: 'bookings', maxSelect: 1 }
    },
    {
      name: 'renter',
      type: 'relation', 
      required: true,
      options: { collectionId: 'users', maxSelect: 1 }
    },
    {
      name: 'venue',
      type: 'relation',
      required: true,
      options: { collectionId: 'venues', maxSelect: 1 }
    },
    {
      name: 'rating',
      type: 'number',
      required: true,
      options: { min: 1, max: 5 }
    },
    {
      name: 'comment',
      type: 'text',
      required: true,
      options: { min: 10, max: 1000 }
    },
    {
      name: 'owner_response',
      type: 'text',
      required: false,
      options: { max: 500 }
    }
  ]
}
```

### Security Rules

- **Create Rule**: `@request.auth.id = @request.data.renter && @request.data.booking.status = "completed"`
- **Read Rule**: Public access (empty string)
- **Update Rule**: `@request.auth.id = venue.owner && owner_response = ""`
- **Delete Rule**: `@request.auth.id != ""` (admin only)

### Venues Collection Updates

Added fields to cache aggregated review data:
- `average_rating` (number, 0-5)
- `review_count` (number, min: 0)

## Components

### Core Components

1. **ReviewForm** (`src/components/venues/ReviewForm.tsx`)
   - Modal component for submitting reviews
   - Integrates StarRating component
   - Form validation and error handling
   - Follows design.json modal styling

2. **ReviewCard** (`src/components/venues/ReviewCard.tsx`)
   - Displays individual reviews with user info
   - Shows nested owner responses
   - Allows owners to add responses
   - Responsive design with proper spacing

3. **ReviewList** (`src/components/venues/ReviewList.tsx`)
   - Displays paginated list of reviews
   - Shows aggregate rating statistics
   - Lazy loading with fade-in animations
   - Rating distribution visualization

4. **ReviewPromptBanner** (`src/components/dashboard/ReviewPromptBanner.tsx`)
   - Dashboard banner prompting users to review completed bookings
   - Dismissible with localStorage persistence
   - Slide-down animation as specified in design
   - Primary green background with white text

### Enhanced Components

5. **StarRating** (Enhanced)
   - Added pop animation on click
   - Updated to use secondary.yellow color (#F59E0B)
   - Improved accessibility with data attributes
   - Smooth transitions and hover effects

## API Functions

### PocketBase Integration (`src/lib/pocketbase.ts`)

- `createReview(reviewData)` - Submit new review
- `getVenueReviews(venueId, page, perPage)` - Get paginated reviews
- `addOwnerResponse(reviewId, responseData)` - Add owner response
- `getVenueRatingStats(venueId)` - Get aggregated rating statistics
- `updateVenueRating(venueId)` - Update cached venue rating
- `getEligibleBookingsForReview(userId)` - Get bookings eligible for review

## Type Definitions

### Review Types (`src/types/review.ts`)

- `Review` - Base review interface
- `ReviewWithExpanded` - Review with expanded relations
- `ReviewFormData` - Form submission data
- `ReviewListResponse` - Paginated response
- `ReviewStats` - Aggregated statistics
- `ReviewPrompt` - Dashboard prompt data

## User Flows

### 1. Leaving a Review

1. User completes a booking (status = "completed")
2. ReviewPromptBanner appears on dashboard
3. User clicks "Leave Review" button
4. ReviewForm modal opens with venue name
5. User selects star rating (1-5) with pop animation
6. User writes comment (min 10 characters)
7. Form validates and submits to PocketBase
8. Venue's average rating is automatically updated

### 2. Owner Response

1. Owner views reviews on their venue page
2. Clicks "Respond to this review" button
3. Response form appears inline
4. Owner writes response (max 500 characters)
5. Response is saved and displayed nested under review
6. Owner can only respond once per review

### 3. Viewing Reviews

1. Reviews displayed on venue detail pages
2. Aggregate rating shown with star visualization
3. Rating distribution chart
4. Paginated list of individual reviews
5. Lazy loading with fade-in animations

## Design System Compliance

### Colors
- Primary green (#059669) for prompts and highlights
- Secondary yellow (#F59E0B) for star ratings
- Neutral colors for text and backgrounds

### Typography
- Poppins for headings
- Roboto for body text
- Consistent font weights and sizes

### Animations
- Star pop animation (0.3s cubic-bezier)
- Slide-down for prompts (0.4s ease-out)
- Fade-in for reviews (0.3s ease-in-out)

### Spacing
- 8px base unit system
- Consistent padding and margins
- Proper component spacing

## Setup Instructions

### 1. Database Setup

Run the setup script to create the reviews collection:

```bash
node scripts/setup-reviews-collection.js
```

Set environment variables:
```bash
POCKETBASE_ADMIN_EMAIL=<EMAIL>
POCKETBASE_ADMIN_PASSWORD=your-admin-password
```

### 2. Integration Points

The review system is integrated into:

- **Dashboard** (`src/pages/dashboard/index.astro`) - ReviewPromptBanner
- **Venue Details** (`src/pages/venues/[id].astro`) - ReviewList
- **Venue Cards** (`src/components/venues/VenueCard.tsx`) - Rating display

### 3. CSS Animations

Custom animations added to `src/styles/global.css`:
- `@keyframes star-pop` - Star rating animation
- `@keyframes slide-down` - Banner animation
- Utility classes for consistent animations

## Security Considerations

1. **XSS Protection** - All user content is properly sanitized
2. **Authorization** - API rules prevent unauthorized actions
3. **Rate Limiting** - One review per booking constraint
4. **Input Validation** - Client and server-side validation

## Testing

### Manual Testing Checklist

- [ ] Review submission for completed bookings
- [ ] Owner response functionality
- [ ] Review display on venue pages
- [ ] Dashboard prompt behavior
- [ ] Star rating animations
- [ ] Form validation and error handling
- [ ] Responsive design on mobile/tablet

### Security Testing

- [ ] Unauthorized review creation attempts
- [ ] Multiple review attempts for same booking
- [ ] Owner response limitations
- [ ] XSS prevention in comments

## Future Enhancements

1. **Review Moderation** - Admin tools for managing inappropriate content
2. **Review Filtering** - Sort by rating, date, etc.
3. **Review Photos** - Allow users to upload images with reviews
4. **Review Helpfulness** - Voting system for helpful reviews
5. **Email Notifications** - Notify owners of new reviews
6. **Review Analytics** - Dashboard for venue owners

## Performance Considerations

1. **Cached Ratings** - Venue average ratings cached for fast loading
2. **Pagination** - Reviews loaded in chunks to prevent performance issues
3. **Lazy Loading** - Reviews fade in as user scrolls
4. **Optimized Queries** - Efficient PocketBase queries with proper indexing

## Conclusion

The reviews and ratings feature provides a comprehensive trust-building system for the Trodoo platform. It follows the established design system, implements proper security measures, and provides an excellent user experience with smooth animations and responsive design.
