// Centralized dependency management for Deno Venue Services

// Web framework
export { Application, Router, Context } from "https://deno.land/x/oak@v12.6.1/mod.ts";
export type { Middleware } from "https://deno.land/x/oak@v12.6.1/mod.ts";

// HTTP utilities
export { Status } from "https://deno.land/std@0.208.0/http/status.ts";
export { serve } from "https://deno.land/std@0.208.0/http/server.ts";

// Crypto for webhook verification
export { crypto } from "https://deno.land/std@0.208.0/crypto/mod.ts";
export { encode as hexEncode } from "https://deno.land/std@0.208.0/encoding/hex.ts";

// Environment variables
export { load } from "https://deno.land/std@0.208.0/dotenv/mod.ts";

// Date utilities
export { format as formatDate } from "https://deno.land/std@0.208.0/datetime/mod.ts";

// Logging
export * as log from "https://deno.land/std@0.208.0/log/mod.ts";

// JSON utilities
export { parse as parseJSON, stringify as stringifyJSON } from "https://deno.land/std@0.208.0/json/mod.ts";

// PocketBase SDK
export { default as PocketBase } from "https://deno.land/x/pocketbase@0.19.0/mod.ts";
export type { RecordModel, AuthModel } from "https://deno.land/x/pocketbase@0.19.0/mod.ts";

// MeiliSearch client
export { MeiliSearch } from "https://esm.sh/meilisearch@0.35.0";

// Cron for scheduled tasks
export { cron } from "https://deno.land/x/deno_cron@v1.0.0/cron.ts";

// Email service (Resend)
export { Resend } from "https://esm.sh/resend@2.0.0";

// Validation
export { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// CORS middleware
export { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

// Rate limiting
export { RateLimiterMemory } from "https://esm.sh/rate-limiter-flexible@3.0.8";

// UUID generation
export { v4 as generateUUID } from "https://deno.land/std@0.208.0/uuid/mod.ts";

// Base64 encoding/decoding
export { encode as base64Encode, decode as base64Decode } from "https://deno.land/std@0.208.0/encoding/base64.ts";

// MJML for email templates (if needed)
export { default as mjml } from "https://esm.sh/mjml@4.14.1";

// Delay utility for retries
export { delay } from "https://deno.land/std@0.208.0/async/delay.ts";
