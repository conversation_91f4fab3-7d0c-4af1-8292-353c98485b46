# Server Configuration
PORT=8000
NODE_ENV=development

# PocketBase Configuration
POCKETBASE_URL=http://localhost:8090
POCKETBASE_ADMIN_EMAIL=<EMAIL>
POCKETBASE_ADMIN_PASSWORD=your_admin_password

# MeiliSearch Configuration
MEILISEARCH_HOST=http://localhost:7700
MEILISEARCH_API_KEY=your_meilisearch_master_key

# Email Service Configuration (Resend)
RESEND_API_KEY=re_your_resend_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=Trodoo

# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
PAYSTACK_WEBHOOK_SECRET=your_webhook_secret

# Frontend URL for email links
FRONTEND_URL=http://localhost:4321

# Logging Configuration
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cron Job Configuration
ENABLE_CRON_JOBS=true
PAYOUT_CRON_SCHEDULE=0 2 * * *
REMINDER_CRON_SCHEDULE=0 * * * *
CHECKIN_REMINDER_CRON_SCHEDULE=*/15 * * * *
REVIEW_PROMPT_CRON_SCHEDULE=0 10 * * *

# Security
WEBHOOK_TIMEOUT_MS=30000
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000

# Database Connection Pool (if needed)
DB_POOL_SIZE=10
DB_TIMEOUT_MS=5000
