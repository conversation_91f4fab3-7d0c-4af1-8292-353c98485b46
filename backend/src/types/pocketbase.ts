// PocketBase type definitions for Trodoo venue services
import type { RecordModel } from "../../deps.ts";

// Base PocketBase record interface
export interface BaseRecord extends RecordModel {
  id: string;
  created: string;
  updated: string;
}

// User record from PocketBase auth collection
export interface User extends BaseRecord {
  email: string;
  name?: string;
  avatar?: string;
  roles: string[];
  is_active: boolean;
  verified: boolean;
  paystack_customer_id?: string;
  paystack_recipient_code?: string;
}

// Venue record
export interface Venue extends BaseRecord {
  title: string;
  description: string;
  address: {
    street: string;
    city: string;
    state?: string;
    country: string;
    postal_code?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  capacity: number;
  price_per_hour: number;
  amenities: string[];
  standard_photos: string[];
  pano_photo?: string;
  rental_agreement_pdf?: string;
  is_published: boolean;
  owner: string; // User ID
  move_out_checklist?: Array<{
    id: string;
    text: string;
  }>;
  average_rating?: number;
  review_count?: number;
}

// Booking record
export interface Booking extends BaseRecord {
  renter: string; // User ID
  owner: string; // User ID
  venue: string; // Venue ID
  start_date: string;
  end_date: string;
  total_price: number;
  platform_fee: number;
  payout_amount: number;
  status: "pending" | "confirmed" | "paid" | "completed" | "cancelled" | "denied";
  paystack_ref?: string;
  special_requests?: string;
}

// Message record
export interface Message extends BaseRecord {
  booking: string; // Booking ID
  sender: string; // User ID
  content: string;
}

// Review record
export interface Review extends BaseRecord {
  booking: string; // Booking ID
  renter: string; // User ID
  venue: string; // Venue ID
  rating: number;
  comment?: string;
  owner_response?: string;
}

// Checklist submission record
export interface ChecklistSubmission extends BaseRecord {
  booking: string; // Booking ID
  renter: string; // User ID
  submission_data: Array<{
    id: string;
    text: string;
    completed: boolean;
  }>;
  notes?: string;
}

// Notification record
export interface Notification extends BaseRecord {
  booking?: string; // Booking ID (optional for some notification types)
  user: string; // User ID
  type: NotificationType;
  sent_at: string;
}

// Notification types
export type NotificationType = 
  | "welcome"
  | "booking_confirmed"
  | "booking_denied"
  | "payment_success"
  | "checkin_reminder"
  | "checkout_reminder"
  | "review_prompt"
  | "invitation_sent"
  | "payout_processed";

// Flagged content record
export interface FlaggedContent extends BaseRecord {
  content_type: "venue" | "user_profile";
  content_id: string;
  reporter: string; // User ID
  reason: string;
  status: "open" | "resolved";
}

// Expanded records with relations
export interface BookingWithRelations extends Booking {
  renter_expand?: User;
  owner_expand?: User;
  venue_expand?: Venue;
}

export interface VenueWithRelations extends Venue {
  owner_expand?: User;
}

export interface MessageWithRelations extends Message {
  sender_expand?: User;
  booking_expand?: BookingWithRelations;
}

export interface ReviewWithRelations extends Review {
  renter_expand?: User;
  venue_expand?: VenueWithRelations;
  booking_expand?: BookingWithRelations;
}

// PocketBase collection names
export const COLLECTIONS = {
  USERS: "users",
  VENUES: "venues",
  BOOKINGS: "bookings",
  MESSAGES: "messages",
  REVIEWS: "reviews",
  CHECKLIST_SUBMISSIONS: "checklist_submissions",
  NOTIFICATIONS: "notifications",
  FLAGGED_CONTENT: "flagged_content",
} as const;

// PocketBase subscription event types
export type SubscriptionEvent = "create" | "update" | "delete";

// PocketBase real-time subscription data
export interface SubscriptionData<T = BaseRecord> {
  action: SubscriptionEvent;
  record: T;
}

// PocketBase query options
export interface QueryOptions {
  filter?: string;
  sort?: string;
  expand?: string;
  page?: number;
  perPage?: number;
}

// PocketBase API response
export interface PocketBaseResponse<T = BaseRecord> {
  page: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
  items: T[];
}

// Error response from PocketBase
export interface PocketBaseError {
  code: number;
  message: string;
  data?: Record<string, unknown>;
}
