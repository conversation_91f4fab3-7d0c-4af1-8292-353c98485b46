// Paystack API type definitions for Trodoo venue services

// Paystack webhook event types
export type PaystackEventType = 
  | "charge.success"
  | "charge.failed"
  | "transfer.success"
  | "transfer.failed"
  | "transfer.reversed";

// Paystack webhook payload
export interface PaystackWebhookPayload {
  event: PaystackEventType;
  data: PaystackChargeData | PaystackTransferData;
}

// Paystack charge data (for payments)
export interface PaystackChargeData {
  id: number;
  domain: string;
  status: "success" | "failed" | "abandoned";
  reference: string;
  amount: number;
  message: string | null;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  channel: string;
  currency: string;
  ip_address: string;
  metadata: {
    booking_id: string;
    venue_id: string;
    renter_id: string;
    owner_id: string;
    custom_fields?: Array<{
      display_name: string;
      variable_name: string;
      value: string;
    }>;
  };
  customer: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    customer_code: string;
    phone: string | null;
    metadata: Record<string, unknown>;
  };
  authorization: {
    authorization_code: string;
    bin: string;
    last4: string;
    exp_month: string;
    exp_year: string;
    channel: string;
    card_type: string;
    bank: string;
    country_code: string;
    brand: string;
    reusable: boolean;
    signature: string;
  };
}

// Paystack transfer data (for payouts)
export interface PaystackTransferData {
  id: number;
  domain: string;
  amount: number;
  currency: string;
  source: string;
  reason: string;
  recipient: {
    id: number;
    name: string;
    email: string;
    type: string;
    currency: string;
    recipient_code: string;
    active: boolean;
    details: {
      account_number: string;
      account_name: string;
      bank_code: string;
      bank_name: string;
    };
  };
  status: "success" | "failed" | "pending" | "reversed";
  transfer_code: string;
  transferred_at: string | null;
  created_at: string;
  updated_at: string;
}

// Paystack API response wrapper
export interface PaystackResponse<T = unknown> {
  status: boolean;
  message: string;
  data: T;
}

// Paystack transfer recipient
export interface PaystackRecipient {
  type: "nuban" | "mobile_money" | "basa";
  name: string;
  account_number: string;
  bank_code: string;
  currency: string;
  email?: string;
  description?: string;
}

// Paystack transfer request
export interface PaystackTransferRequest {
  source: "balance";
  amount: number;
  recipient: string; // recipient code
  reason: string;
  currency?: string;
  reference?: string;
}

// Paystack bank list response
export interface PaystackBank {
  id: number;
  name: string;
  slug: string;
  code: string;
  longcode: string;
  gateway: string | null;
  pay_with_bank: boolean;
  active: boolean;
  country: string;
  currency: string;
  type: string;
}

// Paystack transaction initialization
export interface PaystackInitializeTransaction {
  email: string;
  amount: number;
  currency?: string;
  reference?: string;
  callback_url?: string;
  plan?: string;
  invoice_limit?: number;
  metadata?: Record<string, unknown>;
  channels?: string[];
  split_code?: string;
  subaccount?: string;
  transaction_charge?: number;
  bearer?: "account" | "subaccount";
}

// Paystack transaction verification response
export interface PaystackVerificationResponse {
  id: number;
  domain: string;
  status: "success" | "failed" | "abandoned";
  reference: string;
  amount: number;
  message: string | null;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  channel: string;
  currency: string;
  ip_address: string;
  metadata: Record<string, unknown>;
  log: {
    start_time: number;
    time_spent: number;
    attempts: number;
    errors: number;
    success: boolean;
    mobile: boolean;
    input: unknown[];
    history: Array<{
      type: string;
      message: string;
      time: number;
    }>;
  };
  fees: number;
  fees_split: unknown;
  customer: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    customer_code: string;
    phone: string | null;
    metadata: Record<string, unknown>;
    risk_action: string;
    international_format_phone: string | null;
  };
  authorization: {
    authorization_code: string;
    bin: string;
    last4: string;
    exp_month: string;
    exp_year: string;
    channel: string;
    card_type: string;
    bank: string;
    country_code: string;
    brand: string;
    reusable: boolean;
    signature: string;
    account_name: string | null;
  };
  plan: unknown;
  split: unknown;
  order_id: unknown;
  paidAt: string;
  createdAt: string;
  requested_amount: number;
  pos_transaction_data: unknown;
  source: unknown;
  fees_breakdown: unknown;
}

// Paystack error response
export interface PaystackError {
  status: false;
  message: string;
  errors?: Record<string, string[]>;
}

// Webhook signature verification
export interface WebhookVerification {
  isValid: boolean;
  error?: string;
}

// Payout processing result
export interface PayoutResult {
  success: boolean;
  transferCode?: string;
  amount?: number;
  recipient?: string;
  error?: string;
  bookingId?: string;
}
