// MeiliSearch synchronization service for venue search
import { PocketBase, MeiliSearch } from "../../deps.ts";
import { createLogger, PerformanceLogger } from "../utils/logger.ts";
import type { 
  Venue, 
  VenueWithRelations,
  SubscriptionData 
} from "../types/pocketbase.ts";

const logger = createLogger("MeiliSearchSync");

interface VenueSearchDocument {
  id: string;
  title: string;
  description: string;
  city: string;
  state?: string;
  country: string;
  full_address: string;
  capacity: number;
  price_per_hour: number;
  amenities: string[];
  is_published: boolean;
  owner_name: string;
  average_rating?: number;
  review_count?: number;
  created: string;
  updated: string;
}

class MeiliSearchSyncService {
  private pb: PocketBase | null = null;
  private meili: MeiliSearch | null = null;
  private isInitialized = false;
  private subscriptions: Array<() => void> = [];
  private readonly indexName = "venues";

  async initialize(): Promise<void> {
    const pbUrl = Deno.env.get("POCKETBASE_URL");
    const adminEmail = Deno.env.get("POCKETBASE_ADMIN_EMAIL");
    const adminPassword = Deno.env.get("POCKETBASE_ADMIN_PASSWORD");
    const meiliHost = Deno.env.get("MEILISEARCH_HOST");
    const meiliApiKey = Deno.env.get("MEILISEARCH_API_KEY");

    if (!pbUrl || !adminEmail || !adminPassword || !meiliHost) {
      throw new Error("Missing required configuration for MeiliSearch sync");
    }

    // Initialize PocketBase
    this.pb = new PocketBase(pbUrl);
    await this.pb.admins.authWithPassword(adminEmail, adminPassword);

    // Initialize MeiliSearch
    this.meili = new MeiliSearch({
      host: meiliHost,
      apiKey: meiliApiKey,
    });

    // Setup index configuration
    await this.setupIndex();

    // Setup real-time subscriptions
    await this.setupSubscriptions();

    // Perform initial sync
    await this.performInitialSync();

    this.isInitialized = true;
    logger.info("MeiliSearch sync service initialized");
  }

  isHealthy(): boolean {
    return this.isInitialized && this.pb !== null && this.meili !== null;
  }

  async cleanup(): Promise<void> {
    // Unsubscribe from all real-time subscriptions
    this.subscriptions.forEach(unsubscribe => unsubscribe());
    this.subscriptions = [];
    
    if (this.pb) {
      this.pb.authStore.clear();
    }
    
    logger.info("MeiliSearch sync service cleaned up");
  }

  private async setupIndex(): Promise<void> {
    if (!this.meili) return;

    try {
      const index = this.meili.index(this.indexName);

      // Configure searchable attributes
      await index.updateSearchableAttributes([
        'title',
        'description',
        'city',
        'state',
        'country',
        'full_address',
        'amenities',
        'owner_name'
      ]);

      // Configure filterable attributes
      await index.updateFilterableAttributes([
        'is_published',
        'capacity',
        'price_per_hour',
        'city',
        'state',
        'country',
        'amenities',
        'average_rating',
        'review_count'
      ]);

      // Configure sortable attributes
      await index.updateSortableAttributes([
        'price_per_hour',
        'capacity',
        'average_rating',
        'review_count',
        'created',
        'updated'
      ]);

      // Configure ranking rules
      await index.updateRankingRules([
        'words',
        'typo',
        'proximity',
        'attribute',
        'sort',
        'exactness',
        'average_rating:desc',
        'review_count:desc'
      ]);

      // Configure displayed attributes
      await index.updateDisplayedAttributes([
        'id',
        'title',
        'description',
        'city',
        'state',
        'country',
        'full_address',
        'capacity',
        'price_per_hour',
        'amenities',
        'owner_name',
        'average_rating',
        'review_count'
      ]);

      logger.info("MeiliSearch index configured", { indexName: this.indexName });
    } catch (error) {
      logger.error("Failed to setup MeiliSearch index", error);
      throw error;
    }
  }

  private async setupSubscriptions(): Promise<void> {
    if (!this.pb) return;

    try {
      // Subscribe to venue changes
      const venueUnsubscribe = await this.pb.collection("venues").subscribe("*",
        (data: SubscriptionData<Venue>) => {
          this.handleVenueChange(data).catch(error => {
            logger.error("Failed to handle venue change", error, { 
              venueId: data.record.id,
              action: data.action 
            });
          });
        }
      );
      this.subscriptions.push(venueUnsubscribe);

      logger.info("MeiliSearch subscriptions established");
    } catch (error) {
      logger.error("Failed to setup MeiliSearch subscriptions", error);
      throw error;
    }
  }

  private async handleVenueChange(data: SubscriptionData<Venue>): Promise<void> {
    const perfLogger = new PerformanceLogger(`handleVenueChange:${data.action}`);
    
    try {
      switch (data.action) {
        case "create":
        case "update":
          await this.syncVenueToMeiliSearch(data.record.id);
          break;
        case "delete":
          await this.deleteVenueFromMeiliSearch(data.record.id);
          break;
      }

      perfLogger.end({ venueId: data.record.id });
      logger.info("Venue sync completed", { 
        venueId: data.record.id, 
        action: data.action 
      });
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to sync venue change", error, { 
        venueId: data.record.id,
        action: data.action 
      });
    }
  }

  private async syncVenueToMeiliSearch(venueId: string): Promise<void> {
    if (!this.pb || !this.meili) return;

    try {
      // Get venue with owner information
      const venue = await this.pb.collection("venues").getOne(venueId, {
        expand: "owner"
      }) as VenueWithRelations;

      // Only sync published venues
      if (!venue.is_published) {
        await this.deleteVenueFromMeiliSearch(venueId);
        return;
      }

      // Transform venue data for search
      const searchDocument: VenueSearchDocument = {
        id: venue.id,
        title: venue.title,
        description: venue.description,
        city: typeof venue.address === 'string' ? '' : venue.address.city,
        state: typeof venue.address === 'string' ? '' : venue.address.state,
        country: typeof venue.address === 'string' ? '' : venue.address.country,
        full_address: typeof venue.address === 'string' 
          ? venue.address 
          : `${venue.address.street}, ${venue.address.city}, ${venue.address.state || ''} ${venue.address.country}`.trim(),
        capacity: venue.capacity,
        price_per_hour: venue.price_per_hour,
        amenities: venue.amenities || [],
        is_published: venue.is_published,
        owner_name: venue.owner_expand?.name || '',
        average_rating: venue.average_rating,
        review_count: venue.review_count,
        created: venue.created,
        updated: venue.updated,
      };

      // Add/update document in MeiliSearch
      const index = this.meili.index(this.indexName);
      await index.addDocuments([searchDocument]);

      logger.debug("Venue synced to MeiliSearch", { venueId, title: venue.title });
    } catch (error) {
      logger.error("Failed to sync venue to MeiliSearch", error, { venueId });
      throw error;
    }
  }

  private async deleteVenueFromMeiliSearch(venueId: string): Promise<void> {
    if (!this.meili) return;

    try {
      const index = this.meili.index(this.indexName);
      await index.deleteDocument(venueId);

      logger.debug("Venue deleted from MeiliSearch", { venueId });
    } catch (error) {
      // Don't throw error if document doesn't exist
      if (error.code !== 'document_not_found') {
        logger.error("Failed to delete venue from MeiliSearch", error, { venueId });
        throw error;
      }
    }
  }

  private async performInitialSync(): Promise<void> {
    const perfLogger = new PerformanceLogger("performInitialSync");
    
    try {
      if (!this.pb || !this.meili) return;

      logger.info("Starting initial MeiliSearch sync");

      // Get all published venues
      const venues = await this.pb.collection("venues").getFullList({
        filter: "is_published = true",
        expand: "owner"
      }) as VenueWithRelations[];

      // Transform all venues for search
      const searchDocuments: VenueSearchDocument[] = venues.map(venue => ({
        id: venue.id,
        title: venue.title,
        description: venue.description,
        city: typeof venue.address === 'string' ? '' : venue.address.city,
        state: typeof venue.address === 'string' ? '' : venue.address.state,
        country: typeof venue.address === 'string' ? '' : venue.address.country,
        full_address: typeof venue.address === 'string' 
          ? venue.address 
          : `${venue.address.street}, ${venue.address.city}, ${venue.address.state || ''} ${venue.address.country}`.trim(),
        capacity: venue.capacity,
        price_per_hour: venue.price_per_hour,
        amenities: venue.amenities || [],
        is_published: venue.is_published,
        owner_name: venue.owner_expand?.name || '',
        average_rating: venue.average_rating,
        review_count: venue.review_count,
        created: venue.created,
        updated: venue.updated,
      }));

      // Batch add all documents
      if (searchDocuments.length > 0) {
        const index = this.meili.index(this.indexName);
        await index.addDocuments(searchDocuments);
      }

      perfLogger.end({ syncedCount: searchDocuments.length });
      logger.info("Initial MeiliSearch sync completed", { 
        syncedCount: searchDocuments.length 
      });
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to perform initial sync", error);
      throw error;
    }
  }

  // Manual sync method for maintenance
  async forceSyncAll(): Promise<{ success: boolean; syncedCount?: number; error?: string }> {
    try {
      await this.performInitialSync();
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Health check method
  async checkIndexHealth(): Promise<{ healthy: boolean; stats?: any; error?: string }> {
    try {
      if (!this.meili) {
        return { healthy: false, error: "MeiliSearch not initialized" };
      }

      const index = this.meili.index(this.indexName);
      const stats = await index.getStats();
      
      return { 
        healthy: true, 
        stats: {
          numberOfDocuments: stats.numberOfDocuments,
          isIndexing: stats.isIndexing,
          fieldDistribution: stats.fieldDistribution
        }
      };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }
}

// Export singleton instance
export const meilisearchSyncService = new MeiliSearchSyncService();
