// Centralized logging utility for Deno Venue Services
import { log } from "../../deps.ts";

// Configure log levels based on environment
const logLevel = Deno.env.get("LOG_LEVEL") || "INFO";

// Setup console handler with custom formatting
await log.setup({
  handlers: {
    console: new log.handlers.ConsoleHandler(logLevel as log.LevelName, {
      formatter: (logRecord) => {
        const timestamp = new Date().toISOString();
        const level = logRecord.levelName.padEnd(7);
        const message = logRecord.msg;
        
        // Add color coding for different log levels
        const colors = {
          DEBUG: "\x1b[36m", // Cyan
          INFO: "\x1b[32m",  // Green
          WARN: "\x1b[33m",  // Yellow
          ERROR: "\x1b[31m", // Red
          CRITICAL: "\x1b[35m", // Magenta
        };
        
        const reset = "\x1b[0m";
        const color = colors[logRecord.levelName as keyof typeof colors] || "";
        
        return `${color}[${timestamp}] ${level}${reset} ${message}`;
      },
    }),
  },
  loggers: {
    default: {
      level: logLevel as log.LevelName,
      handlers: ["console"],
    },
  },
});

// Get the default logger
const logger = log.getLogger();

// Enhanced logging functions with context
export class Logger {
  private context: string;

  constructor(context = "App") {
    this.context = context;
  }

  private formatMessage(message: string, meta?: Record<string, unknown>): string {
    const contextStr = `[${this.context}]`;
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : "";
    return `${contextStr} ${message}${metaStr}`;
  }

  debug(message: string, meta?: Record<string, unknown>) {
    logger.debug(this.formatMessage(message, meta));
  }

  info(message: string, meta?: Record<string, unknown>) {
    logger.info(this.formatMessage(message, meta));
  }

  warn(message: string, meta?: Record<string, unknown>) {
    logger.warning(this.formatMessage(message, meta));
  }

  error(message: string, error?: Error | unknown, meta?: Record<string, unknown>) {
    const errorMeta = error instanceof Error 
      ? { error: error.message, stack: error.stack, ...meta }
      : { error: String(error), ...meta };
    
    logger.error(this.formatMessage(message, errorMeta));
  }

  critical(message: string, error?: Error | unknown, meta?: Record<string, unknown>) {
    const errorMeta = error instanceof Error 
      ? { error: error.message, stack: error.stack, ...meta }
      : { error: String(error), ...meta };
    
    logger.critical(this.formatMessage(message, errorMeta));
  }
}

// Create service-specific loggers
export const createLogger = (context: string) => new Logger(context);

// Default logger instance
export const defaultLogger = new Logger("VenueServices");

// Export the logger for backward compatibility
export { logger };

// Performance logging utility
export class PerformanceLogger {
  private startTime: number;
  private logger: Logger;
  private operation: string;

  constructor(operation: string, context = "Performance") {
    this.operation = operation;
    this.logger = new Logger(context);
    this.startTime = performance.now();
    this.logger.debug(`Started: ${operation}`);
  }

  end(meta?: Record<string, unknown>) {
    const duration = performance.now() - this.startTime;
    this.logger.info(`Completed: ${this.operation}`, { 
      duration: `${duration.toFixed(2)}ms`,
      ...meta 
    });
  }

  endWithError(error: Error | unknown, meta?: Record<string, unknown>) {
    const duration = performance.now() - this.startTime;
    this.logger.error(`Failed: ${this.operation}`, error, { 
      duration: `${duration.toFixed(2)}ms`,
      ...meta 
    });
  }
}

// Request logging middleware helper
export function logRequest(context: string, method: string, url: string, status?: number) {
  const logger = new Logger(context);
  const statusStr = status ? ` [${status}]` : "";
  logger.info(`${method} ${url}${statusStr}`);
}

// Error logging helper
export function logError(context: string, operation: string, error: Error | unknown, meta?: Record<string, unknown>) {
  const logger = new Logger(context);
  logger.error(`${operation} failed`, error, meta);
}

// Success logging helper
export function logSuccess(context: string, operation: string, meta?: Record<string, unknown>) {
  const logger = new Logger(context);
  logger.info(`${operation} completed successfully`, meta);
}
