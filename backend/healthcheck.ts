// Health check script for Docker container
const PORT = Deno.env.get("PORT") || "8000";
const HEALTH_URL = `http://localhost:${PORT}/health`;

try {
  const response = await fetch(HEALTH_URL, {
    method: "GET",
    headers: { "Content-Type": "application/json" }
  });

  if (response.ok) {
    const data = await response.json();
    if (data.status === "healthy") {
      console.log("Health check passed");
      Deno.exit(0);
    } else {
      console.error("Health check failed: service unhealthy");
      Deno.exit(1);
    }
  } else {
    console.error(`Health check failed: HTTP ${response.status}`);
    Deno.exit(1);
  }
} catch (error) {
  console.error(`Health check failed: ${error.message}`);
  Deno.exit(1);
}
