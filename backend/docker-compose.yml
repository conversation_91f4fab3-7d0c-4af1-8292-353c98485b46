# Docker Compose for Deno Venue Services Development
version: '3.8'

services:
  # Main Deno service
  venue-services:
    build: .
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - NODE_ENV=development
      - POCKETBASE_URL=${POCKETBASE_URL}
      - POCKETBASE_ADMIN_EMAIL=${POCKETBASE_ADMIN_EMAIL}
      - POCKETBASE_ADMIN_PASSWORD=${POCKETBASE_ADMIN_PASSWORD}
      - MEILISEARCH_HOST=http://meilisearch:7700
      - MEILISEARCH_API_KEY=${MEILISEARCH_API_KEY:-masterKey}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - FROM_EMAIL=${FROM_EMAIL:-<EMAIL>}
      - FROM_NAME=${FROM_NAME:-Trodoo}
      - PAYSTACK_SECRET_KEY=${PAYSTACK_SECRET_KEY}
      - PAYSTACK_PUBLIC_KEY=${PAYSTACK_PUBLIC_KEY}
      - PAYSTACK_WEBHOOK_SECRET=${PAYSTACK_WEBHOOK_SECRET}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:4321}
      - LOG_LEVEL=INFO
      - ENABLE_CRON_JOBS=true
    depends_on:
      - meilisearch
    volumes:
      - .:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "deno", "run", "--allow-net", "--allow-env", "healthcheck.ts"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MeiliSearch for venue search
  meilisearch:
    image: getmeili/meilisearch:v1.5
    ports:
      - "7700:7700"
    environment:
      - MEILI_MASTER_KEY=${MEILISEARCH_API_KEY:-masterKey}
      - MEILI_ENV=development
      - MEILI_LOG_LEVEL=INFO
    volumes:
      - meilisearch_data:/meili_data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7700/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  meilisearch_data:
  redis_data:

networks:
  default:
    name: trodoo-network
